<?php
require_once('cnt.types.inc.php');
	if(isset($_GET['wst']) && $_GET['wst']==0 ) unset($_GET['wst']);

	if( !isset($_GET['str']) || !dlv_stores_exists($_GET['str']) )
		exit;

	$max = 0;
    // Par défaut, on arrive sur le jour en cours
	if( isset($_GET['date1']) && isdate($_GET['date1']) ){ $_SESSION['datepicker_date1'] = $_GET['date1']; }
	if( isset($_GET['date2']) && isdate($_GET['date2']) ){ $_SESSION['datepicker_date2'] = $_GET['date2']; }
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : date('Y-m-d');
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
    $website = (!isset($wst_id) || $wst_id == 0) ? false : $wst_id;
	$type = stats_stores_contacts_type(isset($_GET['str']) ? $_GET['str'] : 0, $date1, $date2, $website );
	$data = array();
	$cpt = 0;
	while ( $current_type = ria_mysql_fetch_assoc($type)) {
		$my_type = gu_messages_types_get_name($current_type['cnt_type']);
		$data[$cpt]['type'] = $my_type;
		$data[$cpt]['data'] = stats_graphs_get_datas( 'str_contact', $date1, $date2, array('str'=> $_GET['str'],'type'=> $current_type['cnt_type']));
		$cpt++;
	}
	$graph_title = _('Contact(s) du ') . ria_date_format($_SESSION['datepicker_date1']) . ((isset($_SESSION['datepicker_date2']) && $_SESSION['datepicker_date1'] != $_SESSION['datepicker_date2']) ? ' au ' . ria_date_format($_SESSION['datepicker_date2']) : '');
?>			
<script><!--
        $(function () {
            $('#graph-str-contacts').highcharts({  chart: {
                type: "spline",
                plotBorderWidth: 0,
                animation: false,
                events: {
                    load: function (event) {
                        var extremes = this.yAxis[0].getExtremes();
                        if (extremes.dataMax == 0) {
                            this.yAxis[0].setExtremes(0, 5);
                        }
                    }
                }
            },
            credits: {
                enabled: false
            },
            exporting: {
                filename: 'statistiques-str_contacts'
            },
            title: {
               text: '<?php print $graph_title; ?>',
                x: -20
            },
            xAxis: {
            <?php
            if(isset($data[0]['data'])){
                print "categories: [" . '\''.implode('\', \'', array_keys($data[0]['data'])).'\'' . "]";
            }
            ?>
            },
            yAxis: {
                allowDecimals: false,
                title: {
                    text: ''
                },
                min: 0
            },
            legend: {
                layout: 'horizontal',
                align: 'center',
                verticalAlign: 'top',
                borderWidth: 0
            },
            tooltip: {
                shared: true,
                crosshairs: true,
                followTouchMove: true,
                style: {
                    fontSize: "13px"
                },
                formatter: function() {
                    var str = '<span style="font-size: 12px;">'+ this.x +'</span>';

                    $.each(this.points, function(i, point) {
                        str += '<br/><span style="color:'+point.series.color+'">'+ point.series.name +'</span>: <b>'+
                        point.y+'</b>';
                    });

                    return str;
                },
            },
            series: [
<?php
			foreach ($data as $key=>$value) {
				if($key != 0){
					print",";
				}
				print"
				 {
                    name: '" . $value['type'] . "',
                    data: [" .  implode(",", $value['data']) . "]
                 }
                ";
			}
?>            
               
            ]
        });
    });
              
--></script>