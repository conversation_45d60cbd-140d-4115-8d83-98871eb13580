<?php
	// MyPDF_Image
	/*
	*/
	
	require_once('mypdf/MyPDF_Element.php');
	
	class MyPDF_Image extends MyPDF_Element {
	
		// attributs
		
			private	$_height;
			private	$_src;
			private	$_width;
		
		// méthodes
		
			// __construct
			/* Constructeur */
			public function __construct($param) {
				parent::__construct($param);
				
				$this->setHeight(array('height' => $param['height']));
				$this->setSearch(array('src' => $param['src']));
				$this->setWidth(array('width' => $param['width']));
			}
			
			// build
			/* Build */
			public function build($param = array()) {
				$pdf = $param['pdf'];
				
				parent::build($param);
				
				$pdf->Image($this->getSearch(), $this->getLeft(), $this->getTop());
				return $this;
			}
			
			// getHeight
			/* Renvoie la hauteur */
			public function getHeight() {
				return $this->_height;
			}
			
			// getLeft
			/* Renvoie left */
			public function getLeft() {
				return $this->getParent()->getLeft();
			}
			
			// getSearch
			/* Renvoie search */
			public function getSearch() {
				return $this->_src;
			}
			
			// getTop
			/* Renvoie top */
			public function getTop() {
				return $this->getParent()->getTop();
			}
			
			// getWidth
			/* Renvoie la largeur */
			public function getWidth() {
				return $this->_width;
			}
			
			// setHeight
			/* Affecte height */
			public function setHeight($param) {
				$this->_height = $param['height'];
				return $this;
			}
			
			// setSearch
			/* Affecte search */
			public function setSearch($param) {
				$this->_src = $param['src'];
				return $this;
			}
			
			// setWidth
			/* Affecte la largeur */
			public function setWidth($param) {
				$this->_width = $param['width'];
				return $this;
			}
		
	}

