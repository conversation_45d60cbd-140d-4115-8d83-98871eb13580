<?php
	require_once('tnt.filters.inc.php');

	$_GET['flt'] = isset($_GET['flt']) ? $_GET['flt'] : 0;

	// Vérifie que l'utilisateur en cours à accès à cette page
	if( $_GET['flt'] != 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FILTER_EDIT');
	}else{ // $_GET['flt'] == 0
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FILTER_ADD');
	}
	
	// Annulation des modifications
	if( isset($_POST['cancel']) ){
		header('Location: /admin/config/filters/index.php');
		exit;
	}
	
	// Enregistrement le filtre
	if( isset($_POST['save']) ){
		if( !isset($_GET['flt'], $_POST['name'], $_POST['ip']) ){
			$error = _('Une ou plusieurs informations obligatoires sont manquantes.');
		} elseif( trim($_POST['name'])=='' ){
			$error = _('Veuillez renseigner la désignation du filtre.');
		} elseif( !is_array($_POST['ip']) || sizeof($_POST['ip'])!=4 ){
			$error = _('Veuillez renseigner toutes les parties de l\'adresse IP.');
		} else {
			foreach( $_POST['ip'] as $part ){
				if( !is_numeric($part) || $part<0 || $part>255 ){
					$error = _('Chaque partie de l\'adresse IP doit être un entier compris entre 0 et 255.');
				}
			}
			
			$ip = implode('.', $_POST['ip']);
			if( !isset($error) ){
				$sum = 0;
				foreach( $_POST['ip'] as $i ){
					$sum += $i;
				}
				
				$error_ip = false;
				
				if( $sum==0 || $ip=='***************' ){
					$error_ip = true;
				} elseif( !filter_var($ip, FILTER_VALIDATE_IP) ){
					$error_ip = true;
				} elseif( $_POST['ip'][3]==0 || $_POST['ip'][3]==255 ){
					$error_ip = true;
				}
				
				if( $error_ip ){
					$error = str_replace("#param[adresse_ip]#", $ip, _("L'adresse IP \"#param[adresse_ip]#\" est incorrecte. \nUne adresse IP est composée de quatre numéro compris entre 0 et 255, mais ne doit pas finir par 0 ou 255."));	
				}
			}
			
			if( !isset($error) ){
				if( $_GET['flt']==0 && !tnt_filters_add($_POST['name'], $ip) ){
					$error = _("Une erreur inattendue s'est produitre lors de l'enregistrement du filtre. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
				} elseif( $_GET['flt']!=0 && !tnt_filters_update($_GET['flt'], $_POST['name'], $ip) ){
					$error = _("Une erreur inattendue s'est produitre lors de l'enregistrement du filtre. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
				} else {
					header('Location: /admin/config/filters/index.php');
					exit;
				}
			}
		}
	}
	
	// Suppression du filtre
	if( isset($_POST['del']) ){
		if( $_GET['flt']>0 && !tnt_filters_del($_GET['flt']) ){
			$error = _("Une erreur inattendue s'est produitre lors de la suppression du filtre. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
		} else {
			if( $_GET['flt']>0 ){
				header('Location: /admin/config/filters/index.php');
			} else {
				header('Location: /admin/config/filters/edit.php?flt=0');
			}
			exit;
		}
	}
	
	$title = _('Nouveau filtre');
	$flt = array( 'id'=>0, 'name'=>'', 'value'=>'', 'symbol'=>'' );
	
	if( isset($_GET['flt']) && is_numeric($_GET['flt']) && $_GET['flt']>0 ){
		$rflt = tnt_filters_get( $_GET['flt'] );
		if( !$rflt || !ria_mysql_num_rows($rflt) ){
			header('Location: /admin/config/filters/index.php');
			exit;
		}
		
		$flt = ria_mysql_fetch_array( $rflt );
		$title = 'Filtre : '.$flt['name'];
	}
	
	if( isset($_POST) ){
		$flt['name'] 	= isset($_POST['name']) ? $_POST['name'] : $flt['name'];
		$flt['value'] 	= isset($_POST['ip']) ? implode('.', $_POST['ip']) : $flt['value'];
	}

	define('ADMIN_PAGE_TITLE', $title.' - '._('Filtres') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

	if( isset($_GET['flt']) && $_GET['flt']>0 && trim($flt['name'])!='' ){
		$title_filters = _('Adresse IP :').' '.$flt['name'];
	}else{
		$title_filters = _('Nouvelle adresse IP filtrée');
	}
?>
<h2><?php echo htmlspecialchars($title_filters); ?></h2>

<?php
	if( isset($error) ){
		print '<div class="error">'.nl2br( $error ).'</div>';
	}
?>
<form action="/admin/config/filters/edit.php?flt=<?php print $flt['id']; ?>" method="post">
	<input type="hidden" name="flt-id" value="<?php print $flt['id']; ?>" />
    <table id="tnt-filters">
        <tbody>
        	<tr>
            	<td><label for="name"><span class="mandatory">*</span> <?php echo _("Désignation :"); ?></label></td>
                <td><input type="text" name="name" id="name" value="<?php print htmlspecialchars($flt['name']); ?>" /></td>
            </tr>
            <tr>
            	<td><label for="ip0"><span class="mandatory">*</span> <?php echo _("Exclure l'adresse IP :"); ?></label></td>
                <td>
					<?php 
						$vals = array( '', '', '', '' );
						if( trim($flt['value'])!='' ){
							$vals = preg_split( '/\./', $flt['value'] ); 
						}
					?>
                    <input class="blc-ip" type="text" name="ip[]" id="ip0" value="<?php print isset($vals[0]) ? $vals[0] : ''; ?>" maxlength="3" />
                    
                    <label class="label-ip" for="ip1">.</label>
                    <input class="blc-ip" type="text" name="ip[]" id="ip1" value="<?php print isset($vals[1]) ? $vals[1] : ''; ?>" maxlength="3" />
                    
                    <label class="label-ip" for="ip2">.</label>
                    <input class="blc-ip" type="text" name="ip[]" id="ip2" value="<?php print isset($vals[2]) ? $vals[2] : ''; ?>" maxlength="3" />
                   	
                    <label class="label-ip" for="ip3"> .</label>
                    <input class="blc-ip" type="text" name="ip[]" id="ip3" value="<?php print isset($vals[3]) ? $vals[3] : ''; ?>" maxlength="3" />
				</td>
            </tr>
       </tbody>
        <tfoot>
            <tr>
                <td colspan="2">
                	<?php
						if( $flt['id']>0 && gu_user_is_authorized('_RGH_ADMIN_CONFIG_FILTER_DEL') ){
							print '<input class="btn-del" type="submit" name="del" value="'._('Supprimer').'" />';
						}
					?>
                    <div class="float-right">
                        <input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" />
                        <input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" />
                    </div>
                </td>
            </tr>
        </tfoot>
    </table>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
