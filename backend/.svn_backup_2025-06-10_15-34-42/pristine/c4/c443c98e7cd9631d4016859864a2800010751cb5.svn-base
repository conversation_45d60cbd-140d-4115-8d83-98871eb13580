<?php

function gu_trading_rules_form_view($fld_id=null, $usr_id=null, $gtr_id=null, $price_fld_id=null, $price_value=false, $price_type=false, $gtr_formated_value='', $gtr_value='', $readonly=false){

	$html = "<tr>";

	$field = false;
	if ( $fld_id != null && $fld_id >= 0 ) {
		$fields = fld_fields_get( $fld_id, 0, 0, 0, 0, 0, null, array(), false, array(), null, null, null, true);
		$field = ria_mysql_fetch_assoc($fields);
	}

	if ( $price_type == "percent" ) {
		$price_value = ( $price_value - 1 ) * 100;
	}

	if ($readonly) {

		if ( is_numeric($fld_id) && $fld_id > 0 ) {
			$html .= '	<td><a href="/admin/customers/negotiations/edit.php?usr_id='.$usr_id.'&gtr_id='.$gtr_id.'">'.htmlspecialchars( _($field["name"]) ).''."\n";
			$html .= '		'._('égal(e) à :')."\n";
			$html .= $gtr_formated_value.'</td></a>';
		}else{
			$html .= '<td><a href="/admin/customers/negotiations/edit.php?usr_id='.$usr_id.'&gtr_id='.$gtr_id.'">'._('Tout le catalogue').'</a></td>';
		}

		$t = '';
		if ( $price_fld_id == PRD_PRICE_PUBLIC_HT ) {
			$t = ' public.';
		}elseif ( $price_fld_id == PRD_PRICE_USR_HT ) {
			$t = ' client.';
		}elseif ( $price_fld_id == _FLD_PRD_PURCHASE ) {
			$t = ' d\'achat.';
		}

		if ( $price_type == "percent" ) {
			$html .= '<td colspan="2"> '.sprintf(_('Une promotion maximale de %d %% peut s\'appliquer sur le prix'), round($price_value,2)).' '.$t.'</td>';
		} else if( $price_type == "discount" ){
			$html .= '<td colspan="2"> '.sprintf(_('Une réduction maximale de %d € peut s\'appliquer sur le prix'), round($price_value,2)).' '.$t.'</td>';
		}

	}else{
		$html .= '	<td data-label="'._('Contexte :').' ">
						<select autofocus class="rec-new-fld valign-center" name="rec_new_fld" data-usr_id="'.$usr_id.'" data-fld_id="'.$fld_id.'">
							<option value="-1">'._('Tout le catalogue').'</option>
							<optgroup label="'._('Propriétés sur le produit').'">';
							if( $rfld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), true, array(), null, CLS_PRODUCT, null, true, null, null, false, true ) ){
								while( $fld = ria_mysql_fetch_array($rfld) ){
									$html .= '<option value="'.$fld['id'].'" '.($fld_id == $fld['id'] ? 'selected="selected"':'').'>'.htmlspecialchars($fld['name']).'</option>';
								}
							}
		$html .= '			</optgroup>
							<optgroup label="'._('Propriétés sur la catégorie').'">';
							if( $rfld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), true, array(), null, CLS_CATEGORY, null, true, null, null, false, true ) ){
								while( $fld = ria_mysql_fetch_array($rfld) ){
									$html .= '<option value="'.$fld['id'].'" '.($fld_id == $fld['id'] ? 'selected="selected"':'').'>'.htmlspecialchars($fld['name']).'</option>';
								}
							}
		$html .= '			</optgroup>
							<optgroup label="'._('Propriétés sur la marque').'">';
							if( $rfld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), true, array(), null, CLS_BRAND, null, true, null, null, false, true ) ){
								while( $fld = ria_mysql_fetch_array($rfld) ){
									$html .= '<option value="'.$fld['id'].'" '.($fld_id == $fld['id'] ? 'selected="selected"':'').'>'.htmlspecialchars($fld['name']).'</option>';
								}
							}
		$html .= '			</optgroup>
						</select>';

		// Valeur dynamique en fonction du type texte
		if ($field) {
			switch ($field["type_id"]) {
				case FLD_TYPE_TEXTAREA:
				case FLD_TYPE_TEXT:
					$html .= '	<input class="little_input"  type="text" name="gtr_value" value="'.$gtr_formated_value.'" placeholder="'.htmlspecialchars($fld['name']).'" required>';
					break;
				case FLD_TYPE_BOOLEAN_YES_NO:
					$html .= '
								<input type="radio"  name="gtr_value" value="1" '.(($gtr_formated_value == "Oui") ? 'checked="checked"':'').' required> '._('Oui').'
								<input type="radio"  name="gtr_value" value="0" '.(($gtr_formated_value == "Non") ? 'checked="checked"':'').' required> '._('Non').'<br>
							  ';
					break;
				case FLD_TYPE_SELECT:
				case FLD_TYPE_SELECT_HIERARCHY:
					$select_options = fld_fields_get_values_array($fld_id, 0, true, false, false, false);
					$html .= '<select required class="little_input valign-center" name="gtr_value">';
					if ($select_options != null && count($select_options) > 0 ) {
						asort($select_options);
						while (list($key,$value) = each($select_options)) {
							$value_id = fld_restricted_values_get_id($fld_id, $value);
							if ($value_id) {
								$html .= '<option value="'.$value_id.'">'.$value.'</option>';
							}
						}
					}else{
						$html .= '<option value="">'._('Aucune sélection').'</option>';
					}
					$html .='</select>';
					break;
				case FLD_TYPE_REFERENCES_ID:
					$class = '';
					switch ($fld_id) {
							case _FLD_PRD_ID:
									$class = 'add-rule-prd';
								break;
							case _FLD_CAT_ID:
									$class = 'add-rule-cat';
								break;
							case _FLD_BRD_ID:
									$class = 'add-rule-brd';
								break;
						}

					$html .= '	<input type="hidden" name="gtr_value" id="gtr_value-'.$fld_id.'" value="'.$gtr_value.'">
								<input class="little_input '.$class.' valign-center" name="name-gtr_value-'.$fld_id.'" data-input="gtr_value-'.$fld_id.'" type="text" value="'.$gtr_formated_value.'" placeholder="'.htmlspecialchars($fld['name']).'" required>
								<input class="'.$class.'" type="button" name="cls-ref-select" id="choose-rec-usr" value="'._('Choisir').'" />
								';


					break;

				case FLD_TYPE_FLOAT:
					$html .= '<input class="little_input valign-center" type="number" step="0.01" name="gtr_value" value="'._('Champ non pris en charge').'" placeholder="0.00">';
					break;

				case FLD_TYPE_INT:
					$html .= '<input class="little_input valign-center" type="number" name="gtr_value" value="'._('Champ non pris en charge').'" placeholder="0">';
					break;

				default:
					$html .= '<input class="little_input valign-center" type="text" name="gtr_value" value="NC: '.$field["type_id"].'" placeholder="'.htmlspecialchars($fld['name']).'">';
					break;
			}
		}else{
			$html .= '<input type="hidden" name="gtr_value" value="null">';
		}

		$html .= '<input type="hidden" name="gtr_id" value="'.$gtr_id.'">';

		if ($usr_id != null) {
			$html .= '<input type="hidden" name="usr_id" value="'.$usr_id.'">';
		}


		if ($gtr_id != null) {
			$html .= '<input type="hidden" name="gtr_id" value="'.$gtr_id.'">';
		}

		// Select price HT ou ttc
		$html .= '	<td data-label="'._('Règle :').' ">
						Prix :
						<select class="little_input valign-center" name="price_fld_id">
							<option value="'.PRD_PRICE_PUBLIC_HT.'" '.(($price_fld_id == PRD_PRICE_PUBLIC_HT) ? 'selected':'').' >'._('Public').'</option>
							<option value="'.PRD_PRICE_USR_HT.'" '.(($price_fld_id == PRD_PRICE_USR_HT) ? 'selected':'').' >'._('Client').'</option>
							<option value="'._FLD_PRD_PURCHASE.'" '.(($price_fld_id == _FLD_PRD_PURCHASE) ? 'selected':'').' >'._('d\'Achat').'</option>
						</select>
					';

		// Input type number valeur pour
		$html .= ' <td>';
		if ( isset($price_value) && $price_value != false ) {

			$html .= '	<input class="little_input valign-center" type="text" name="price_value" value="'.round( $price_value, 2).'" required />';

		}else{
			$html .= '	<input class="little_input valign-center" type="text" name="price_value" value="" placeholder="'._('Valeur').'" required />';
		}

		// Select poucentage ou valeur(€)
		$html .= '
						<input id="percent_radio" type="radio" name="price_type" value="percent" '.(($price_type == "percent") ? 'checked="checked"' : '').' required/>
						<label for="percent_radio">%</label>

						<input id="discount_radio" type="radio" name="price_type" value="discount" '.(($price_type == "discount") ? 'checked="checked"' : '').' required/>
						<label for="discount_radio">€ '._('HT').'</label>
					</td>';

	}

	if ($gtr_id != null) {
		$html .= '	<td>
						<a class="del button" href="edit.php?action=del_gtr&gtr_id='.$gtr_id.'">'._('Supprimer').'</a>
					</td>';
	}

	$html .= '</tr>';

	return $html;

}

