// Confluence API utilities for interacting with Confluence REST API
class ConfluenceAPI {
    constructor(baseUrl, apiPath = '/rest/api') {
        this.baseUrl = baseUrl;
        this.apiUrl = baseUrl + apiPath;
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.rateLimit = 3; // requests per second
        this.lastRequestTime = 0;
    }

    // Set rate limit for API requests
    setRateLimit(requestsPerSecond) {
        this.rateLimit = requestsPerSecond;
    }

    // Make authenticated API request
    async makeRequest(endpoint, options = {}) {
        const url = `${this.apiUrl}${endpoint}`;
        
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Atlassian-Token': 'no-check' // CSRF protection
            },
            credentials: 'same-origin'
        };

        const requestOptions = { ...defaultOptions, ...options };

        // Apply rate limiting
        await this.rateLimitDelay();

        try {
            const response = await fetch(url, requestOptions);
            
            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }

            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        } catch (error) {
            console.error(`API request error for ${endpoint}:`, error);
            throw error;
        }
    }

    // Get all spaces with pagination
    async getAllSpaces() {
        const spaces = [];
        let start = 0;
        const limit = 50;
        let hasMore = true;

        while (hasMore) {
            try {
                const response = await this.makeRequest(
                    `/space?limit=${limit}&start=${start}&expand=description,homepage`
                );

                spaces.push(...response.results);
                start += limit;
                hasMore = response.size === limit;
            } catch (error) {
                console.error('Error fetching spaces:', error);
                break;
            }
        }

        return spaces;
    }

    // Get all pages in a space
    async getSpacePages(spaceKey, options = {}) {
        const pages = [];
        let start = 0;
        const limit = options.limit || 50;
        let hasMore = true;

        const expand = options.expand || 'version,space,ancestors';

        while (hasMore) {
            try {
                const response = await this.makeRequest(
                    `/space/${spaceKey}/content?limit=${limit}&start=${start}&expand=${expand}`
                );

                pages.push(...response.results);
                start += limit;
                hasMore = response.size === limit;
            } catch (error) {
                console.error(`Error fetching pages for space ${spaceKey}:`, error);
                break;
            }
        }

        return pages;
    }

    // Get page content with full details
    async getPageContent(pageId, expand = 'body.storage,version,space,ancestors,children.attachment') {
        try {
            return await this.makeRequest(`/content/${pageId}?expand=${expand}`);
        } catch (error) {
            console.error(`Error fetching content for page ${pageId}:`, error);
            throw error;
        }
    }

    // Get page attachments
    async getPageAttachments(pageId) {
        try {
            const response = await this.makeRequest(
                `/content/${pageId}/child/attachment?expand=version,container,metadata.mediaType`
            );
            return response.results || [];
        } catch (error) {
            console.error(`Error fetching attachments for page ${pageId}:`, error);
            return [];
        }
    }

    // Download attachment content
    async downloadAttachment(attachmentId, downloadUrl) {
        try {
            // Use the download URL provided by Confluence
            const response = await fetch(downloadUrl, {
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`Failed to download attachment: ${response.status}`);
            }

            return await response.blob();
        } catch (error) {
            console.error(`Error downloading attachment ${attachmentId}:`, error);
            throw error;
        }
    }

    // Get page children (sub-pages)
    async getPageChildren(pageId) {
        try {
            const response = await this.makeRequest(
                `/content/${pageId}/child/page?expand=version,space,ancestors`
            );
            return response.results || [];
        } catch (error) {
            console.error(`Error fetching children for page ${pageId}:`, error);
            return [];
        }
    }

    // Search for content
    async searchContent(query, options = {}) {
        const searchParams = new URLSearchParams({
            cql: query,
            limit: options.limit || 50,
            start: options.start || 0,
            expand: options.expand || 'version,space'
        });

        try {
            const response = await this.makeRequest(`/content/search?${searchParams}`);
            return response.results || [];
        } catch (error) {
            console.error('Error searching content:', error);
            return [];
        }
    }

    // Get space content tree (hierarchical structure)
    async getSpaceContentTree(spaceKey) {
        try {
            // Get all pages in the space
            const pages = await this.getSpacePages(spaceKey, {
                expand: 'version,space,ancestors'
            });

            // Build hierarchical tree
            const tree = this.buildContentTree(pages);
            return tree;
        } catch (error) {
            console.error(`Error building content tree for space ${spaceKey}:`, error);
            return [];
        }
    }

    // Build hierarchical tree from flat page list
    buildContentTree(pages) {
        const pageMap = new Map();
        const rootPages = [];

        // Create map of all pages
        pages.forEach(page => {
            pageMap.set(page.id, {
                ...page,
                children: []
            });
        });

        // Build tree structure
        pages.forEach(page => {
            const pageNode = pageMap.get(page.id);
            
            if (page.ancestors && page.ancestors.length > 0) {
                // Find immediate parent
                const parentId = page.ancestors[page.ancestors.length - 1].id;
                const parent = pageMap.get(parentId);
                
                if (parent) {
                    parent.children.push(pageNode);
                } else {
                    // Parent not in current space, treat as root
                    rootPages.push(pageNode);
                }
            } else {
                // No ancestors, this is a root page
                rootPages.push(pageNode);
            }
        });

        return rootPages;
    }

    // Get user information
    async getCurrentUser() {
        try {
            return await this.makeRequest('/user/current');
        } catch (error) {
            console.error('Error fetching current user:', error);
            return null;
        }
    }

    // Get space permissions for current user
    async getSpacePermissions(spaceKey) {
        try {
            return await this.makeRequest(`/space/${spaceKey}/permission`);
        } catch (error) {
            console.error(`Error fetching permissions for space ${spaceKey}:`, error);
            return null;
        }
    }

    // Rate limiting implementation
    async rateLimitDelay() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        const minInterval = 1000 / this.rateLimit; // milliseconds between requests

        if (timeSinceLastRequest < minInterval) {
            const delay = minInterval - timeSinceLastRequest;
            await new Promise(resolve => setTimeout(resolve, delay));
        }

        this.lastRequestTime = Date.now();
    }

    // Batch process multiple API requests with rate limiting
    async batchProcess(requests, processor) {
        const results = [];
        
        for (let i = 0; i < requests.length; i++) {
            try {
                const result = await processor(requests[i], i);
                results.push(result);
            } catch (error) {
                console.error(`Error processing batch item ${i}:`, error);
                results.push({ error: error.message });
            }
            
            // Apply rate limiting between requests
            if (i < requests.length - 1) {
                await this.rateLimitDelay();
            }
        }

        return results;
    }

    // Detect Confluence instance type and version
    async detectInstanceInfo() {
        try {
            // Try to get server info
            const serverInfo = await this.makeRequest('/settings/systemInfo');
            
            return {
                type: serverInfo.cloudId ? 'cloud' : 'server',
                version: serverInfo.version,
                baseUrl: serverInfo.baseUrl
            };
        } catch (error) {
            // Fallback detection based on URL patterns
            if (window.location.hostname.includes('.atlassian.net')) {
                return { type: 'cloud', version: 'unknown', baseUrl: this.baseUrl };
            } else {
                return { type: 'server', version: 'unknown', baseUrl: this.baseUrl };
            }
        }
    }

    // Validate API connectivity
    async validateConnection() {
        try {
            await this.makeRequest('/space?limit=1');
            return { valid: true, message: 'Connection successful' };
        } catch (error) {
            return { 
                valid: false, 
                message: `Connection failed: ${error.message}`,
                error: error
            };
        }
    }
}
