#!/bin/bash

# Validation script for Confluence Exporter Chrome Extension
# Checks if all required files are present and properly formatted

echo "🔍 Validating Confluence Exporter Chrome Extension..."
echo "=================================================="

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Counters
errors=0
warnings=0
checks=0

# Function to check if file exists
check_file() {
    local file=$1
    local description=$2
    checks=$((checks + 1))
    
    if [ -f "$file" ]; then
        echo -e "✅ ${GREEN}$description${NC}"
        return 0
    else
        echo -e "❌ ${RED}$description - Missing!${NC}"
        errors=$((errors + 1))
        return 1
    fi
}

# Function to check directory
check_directory() {
    local dir=$1
    local description=$2
    checks=$((checks + 1))
    
    if [ -d "$dir" ]; then
        echo -e "✅ ${GREEN}$description${NC}"
        return 0
    else
        echo -e "❌ ${RED}$description - Missing!${NC}"
        errors=$((errors + 1))
        return 1
    fi
}

# Function to validate JSON
validate_json() {
    local file=$1
    checks=$((checks + 1))
    
    if command -v python3 &> /dev/null; then
        if python3 -m json.tool "$file" > /dev/null 2>&1; then
            echo -e "✅ ${GREEN}$file - Valid JSON${NC}"
            return 0
        else
            echo -e "❌ ${RED}$file - Invalid JSON syntax${NC}"
            errors=$((errors + 1))
            return 1
        fi
    else
        echo -e "⚠️  ${YELLOW}$file - Cannot validate JSON (python3 not available)${NC}"
        warnings=$((warnings + 1))
        return 1
    fi
}

echo ""
echo "📁 Checking core files..."
echo "------------------------"

# Check core extension files
check_file "manifest.json" "Manifest file"
check_file "popup.html" "Popup HTML"
check_file "popup.css" "Popup CSS"
check_file "popup.js" "Popup JavaScript"
check_file "content.js" "Content script"
check_file "background.js" "Background script"
check_file "confluence-api.js" "Confluence API utilities"
check_file "export-utils.js" "Export utilities"

echo ""
echo "🎨 Checking icons..."
echo "-------------------"

# Check icons directory and files
check_directory "icons" "Icons directory"

if [ -d "icons" ]; then
    check_file "icons/icon16.png" "16x16 icon"
    check_file "icons/icon48.png" "48x48 icon"
    check_file "icons/icon128.png" "128x128 icon"
fi

echo ""
echo "🔧 Validating file formats..."
echo "-----------------------------"

# Validate JSON files
if [ -f "manifest.json" ]; then
    validate_json "manifest.json"
fi

echo ""
echo "📋 Checking file permissions..."
echo "-------------------------------"

# Check if files are readable
for file in manifest.json popup.html popup.css popup.js content.js background.js confluence-api.js export-utils.js; do
    if [ -f "$file" ]; then
        if [ -r "$file" ]; then
            echo -e "✅ ${GREEN}$file - Readable${NC}"
        else
            echo -e "❌ ${RED}$file - Not readable${NC}"
            errors=$((errors + 1))
        fi
        checks=$((checks + 1))
    fi
done

echo ""
echo "📊 Checking manifest.json content..."
echo "------------------------------------"

if [ -f "manifest.json" ]; then
    # Check for required manifest fields
    required_fields=("manifest_version" "name" "version" "permissions" "action" "background" "content_scripts")
    
    for field in "${required_fields[@]}"; do
        if grep -q "\"$field\"" manifest.json; then
            echo -e "✅ ${GREEN}Manifest contains '$field'${NC}"
        else
            echo -e "❌ ${RED}Manifest missing '$field'${NC}"
            errors=$((errors + 1))
        fi
        checks=$((checks + 1))
    done
    
    # Check manifest version
    if grep -q "\"manifest_version\": 3" manifest.json; then
        echo -e "✅ ${GREEN}Using Manifest V3${NC}"
    else
        echo -e "⚠️  ${YELLOW}Not using Manifest V3 (may not work in modern Chrome)${NC}"
        warnings=$((warnings + 1))
    fi
    checks=$((checks + 1))
fi

echo ""
echo "🔍 Additional checks..."
echo "----------------------"

# Check for common issues
if [ -f "popup.js" ] && [ -f "content.js" ]; then
    # Check if chrome.runtime.onMessage is used (required for communication)
    if grep -q "chrome.runtime.onMessage" content.js; then
        echo -e "✅ ${GREEN}Message listener found in content script${NC}"
    else
        echo -e "⚠️  ${YELLOW}No message listener found in content script${NC}"
        warnings=$((warnings + 1))
    fi
    checks=$((checks + 1))
fi

# Check file sizes (warn if too large)
for file in *.js *.html *.css; do
    if [ -f "$file" ]; then
        size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
        if [ "$size" -gt 1048576 ]; then  # 1MB
            echo -e "⚠️  ${YELLOW}$file is large (${size} bytes) - may slow extension${NC}"
            warnings=$((warnings + 1))
        fi
    fi
done

echo ""
echo "📈 Validation Summary"
echo "===================="
echo "Total checks: $checks"
echo -e "Errors: ${RED}$errors${NC}"
echo -e "Warnings: ${YELLOW}$warnings${NC}"

if [ $errors -eq 0 ]; then
    echo ""
    echo -e "🎉 ${GREEN}Extension validation passed!${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Open Chrome and go to chrome://extensions/"
    echo "2. Enable 'Developer mode'"
    echo "3. Click 'Load unpacked' and select this directory"
    echo "4. Test on a Confluence page"
    
    if [ $warnings -gt 0 ]; then
        echo ""
        echo -e "⚠️  ${YELLOW}Note: There are $warnings warnings that should be addressed${NC}"
    fi
    
    exit 0
else
    echo ""
    echo -e "❌ ${RED}Extension validation failed with $errors errors${NC}"
    echo ""
    echo "Please fix the errors above before loading the extension."
    echo ""
    echo "Common fixes:"
    echo "- Run './create-icons.sh' to create missing icons"
    echo "- Check file permissions with 'ls -la'"
    echo "- Validate JSON syntax in manifest.json"
    
    exit 1
fi
