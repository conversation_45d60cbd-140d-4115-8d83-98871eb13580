<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM Parser Test - Confluence Exporter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 Confluence Exporter - DOM Parser Test</h1>
    
    <div class="test-section warning">
        <h3>⚠️ Important Notes</h3>
        <ul>
            <li>This test page simulates Confluence DOM structure for testing</li>
            <li>For real testing, navigate to <strong>izberg.atlassian.net</strong></li>
            <li>The extension will only work on actual Confluence pages</li>
            <li>This page helps verify the DOM parsing logic works correctly</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🔍 Test Controls</h3>
        <button onclick="testConfluenceDetection()">Test Confluence Detection</button>
        <button onclick="testSpaceDiscovery()">Test Space Discovery</button>
        <button onclick="testPageParsing()">Test Page Parsing</button>
        <button onclick="testContentExtraction()">Test Content Extraction</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="test-section">
        <h3>📋 Test Results</h3>
        <div id="test-log" class="log">
            <p><em>Click test buttons above to run tests...</em></p>
        </div>
    </div>

    <!-- Simulated Confluence DOM elements for testing -->
    <div style="display: none;">
        <!-- Confluence meta tags -->
        <meta name="confluence-base-url" content="https://izberg.atlassian.net/wiki">
        
        <!-- Confluence navigation -->
        <div id="confluence-navigation">
            <a href="/wiki/spaces/TEST">Test Space</a>
            <a href="/wiki/spaces/DOCS">Documentation</a>
        </div>
        
        <!-- Page content -->
        <div class="wiki-content">
            <h1 id="title-text">Test Page Title</h1>
            <div class="page-content">
                <p>This is test content for the Confluence page.</p>
                <h2>Section 1</h2>
                <p>Some content in section 1.</p>
            </div>
        </div>
        
        <!-- Breadcrumbs -->
        <div class="aui-nav-breadcrumbs">
            <a href="/wiki/spaces/TEST">Test Space</a>
            <a href="/wiki/spaces/TEST/pages/123">Parent Page</a>
        </div>
        
        <!-- Attachments -->
        <div class="attachment-list">
            <a href="/download/attachments/123/document.pdf">document.pdf</a>
            <a href="/download/attachments/123/image.png">image.png</a>
        </div>
    </div>

    <script src="dom-parser.js"></script>
    <script>
        let testLog = document.getElementById('test-log');
        let parser = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            
            if (type === 'success') {
                logEntry.style.color = '#28a745';
            } else if (type === 'error') {
                logEntry.style.color = '#dc3545';
            } else if (type === 'warning') {
                logEntry.style.color = '#ffc107';
            }
            
            testLog.appendChild(logEntry);
            testLog.scrollTop = testLog.scrollHeight;
        }

        function clearLog() {
            testLog.innerHTML = '<p><em>Log cleared...</em></p>';
        }

        function testConfluenceDetection() {
            log('🔍 Testing Confluence Detection...', 'info');
            
            try {
                parser = new ConfluenceDOMParser();
                const isConfluence = parser.isConfluencePage();
                const instanceType = parser.getInstanceType();
                
                if (isConfluence) {
                    log(`✅ Confluence detected: ${instanceType}`, 'success');
                    log(`📍 Base URL: ${parser.baseUrl}`, 'info');
                } else {
                    log('❌ Confluence not detected', 'error');
                }
            } catch (error) {
                log(`❌ Error in detection: ${error.message}`, 'error');
            }
        }

        function testSpaceDiscovery() {
            log('🏢 Testing Space Discovery...', 'info');
            
            if (!parser) {
                log('⚠️ Run Confluence Detection first', 'warning');
                return;
            }
            
            try {
                const spaces = parser.parseSpacesFromCurrentPage();
                log(`📊 Found ${spaces.length} spaces:`, 'info');
                
                spaces.forEach(space => {
                    log(`  • ${space.name} (${space.key})`, 'info');
                });
                
                if (spaces.length > 0) {
                    log('✅ Space discovery successful', 'success');
                } else {
                    log('⚠️ No spaces found - this is expected on test page', 'warning');
                }
            } catch (error) {
                log(`❌ Error in space discovery: ${error.message}`, 'error');
            }
        }

        function testPageParsing() {
            log('📄 Testing Page Parsing...', 'info');
            
            if (!parser) {
                log('⚠️ Run Confluence Detection first', 'warning');
                return;
            }
            
            try {
                const pageContent = parser.parseCurrentPageContent();
                
                log(`📝 Page ID: ${pageContent.id}`, 'info');
                log(`📝 Page Title: ${pageContent.title}`, 'info');
                log(`📝 Space Key: ${pageContent.spaceKey || 'Not detected'}`, 'info');
                log(`📝 Breadcrumbs: ${pageContent.breadcrumbs.length} items`, 'info');
                log(`📎 Attachments: ${pageContent.attachments.length} items`, 'info');
                
                if (pageContent.title) {
                    log('✅ Page parsing successful', 'success');
                } else {
                    log('⚠️ Page parsing incomplete', 'warning');
                }
            } catch (error) {
                log(`❌ Error in page parsing: ${error.message}`, 'error');
            }
        }

        function testContentExtraction() {
            log('📖 Testing Content Extraction...', 'info');
            
            if (!parser) {
                log('⚠️ Run Confluence Detection first', 'warning');
                return;
            }
            
            try {
                const title = parser.extractPageTitle();
                const body = parser.extractPageBody();
                const breadcrumbs = parser.extractBreadcrumbs();
                const attachments = parser.extractAttachments();
                
                log(`📝 Extracted title: "${title}"`, 'info');
                log(`📝 Body content length: ${body.length} characters`, 'info');
                log(`🍞 Breadcrumbs found: ${breadcrumbs.length}`, 'info');
                log(`📎 Attachments found: ${attachments.length}`, 'info');
                
                if (title && body) {
                    log('✅ Content extraction successful', 'success');
                } else {
                    log('⚠️ Content extraction incomplete', 'warning');
                }
            } catch (error) {
                log(`❌ Error in content extraction: ${error.message}`, 'error');
            }
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            log('🚀 DOM Parser Test Page Loaded', 'info');
            log('💡 Click test buttons to verify functionality', 'info');
            log('🌐 For real testing, navigate to izberg.atlassian.net', 'warning');
        });
    </script>
</body>
</html>
