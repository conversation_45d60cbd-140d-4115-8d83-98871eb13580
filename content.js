class ConfluenceExporter {
    constructor() {
        this.isConfluence = false;
        this.instanceType = '';
        this.baseUrl = '';
        this.domParser = null;
        this.exportState = {
            isExporting: false,
            cancelled: false,
            progress: {
                total: 0,
                completed: 0,
                currentPage: '',
                message: 'Initializing...'
            }
        };
        this.exportedPages = [];
        this.discoveredSpaces = [];
        this.rateLimit = 3; // requests per second

        this.init();
    }

    init() {
        this.domParser = new ConfluenceDOMParser();
        this.detectConfluence();
        this.setupMessageListener();
    }

    detectConfluence() {
        // Check for Confluence indicators
        const indicators = [
            // Meta tags
            document.querySelector('meta[name="confluence-base-url"]'),
            document.querySelector('meta[name="ajs-base-url"]'),
            // Confluence-specific elements
            document.querySelector('#confluence-navigation'),
            document.querySelector('.confluence-content'),
            document.querySelector('#main-content.wiki-content'),
            // Confluence JavaScript variables
            window.AJS && window.AJS.Meta,
            // URL patterns
            window.location.href.includes('/wiki/'),
            window.location.href.includes('/confluence/'),
            // Page title patterns
            document.title.includes('Confluence'),
            // Body classes
            document.body.classList.contains('confluence'),
            document.body.classList.contains('wiki-content')
        ];

        this.isConfluence = indicators.some(indicator => indicator);

        if (this.isConfluence) {
            this.determineInstanceType();
            this.setupApiUrl();
        }
    }

    determineInstanceType() {
        // Check if it's Confluence Cloud or Server
        if (window.location.hostname.includes('.atlassian.net')) {
            this.instanceType = 'Confluence Cloud';
        } else if (document.querySelector('meta[name="confluence-base-url"]')) {
            this.instanceType = 'Confluence Server/Data Center';
        } else {
            this.instanceType = 'Confluence (Unknown Version)';
        }

        // Extract base URL
        const baseUrlMeta = document.querySelector('meta[name="confluence-base-url"]') || 
                           document.querySelector('meta[name="ajs-base-url"]');
        
        if (baseUrlMeta) {
            this.baseUrl = baseUrlMeta.getAttribute('content');
        } else {
            // Fallback: construct from current URL
            const url = new URL(window.location.href);
            this.baseUrl = `${url.protocol}//${url.host}`;
            
            // Try to detect context path
            const pathParts = url.pathname.split('/');
            if (pathParts.includes('wiki') || pathParts.includes('confluence')) {
                const contextIndex = pathParts.findIndex(part => part === 'wiki' || part === 'confluence');
                if (contextIndex > 0) {
                    this.baseUrl += '/' + pathParts.slice(1, contextIndex + 1).join('/');
                }
            }
        }
    }

    setupApiUrl() {
        // Setup REST API URL based on instance type
        if (this.instanceType.includes('Cloud')) {
            this.apiUrl = `${this.baseUrl}/wiki/rest/api`;
        } else {
            this.apiUrl = `${this.baseUrl}/rest/api`;
        }
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'detectConfluence':
                    sendResponse({
                        isConfluence: this.isConfluence,
                        instanceType: this.instanceType,
                        baseUrl: this.baseUrl
                    });
                    break;

                case 'getSpaces':
                    const spaces = await this.getSpaces();
                    sendResponse({ spaces });
                    break;

                case 'startExport':
                    this.startExport(request.options);
                    sendResponse({ status: 'started' });
                    break;

                case 'getExportProgress':
                    sendResponse(this.getExportProgress());
                    break;

                case 'cancelExport':
                    this.cancelExport();
                    sendResponse({ status: 'cancelled' });
                    break;

                default:
                    sendResponse({ error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ error: error.message });
        }
    }

    async getSpaces() {
        try {
            const response = await this.makeApiRequest('/space?limit=100&expand=description');
            return response.results.map(space => ({
                key: space.key,
                name: space.name,
                type: space.type,
                description: space.description?.plain?.value || ''
            }));
        } catch (error) {
            console.error('Error fetching spaces:', error);
            throw new Error('Failed to fetch spaces. Please check your permissions.');
        }
    }

    async startExport(options) {
        if (this.exportState.isExporting) {
            throw new Error('Export already in progress');
        }

        this.exportState.isExporting = true;
        this.exportState.cancelled = false;
        this.exportedPages = [];
        this.rateLimit = options.rateLimit || 3;

        try {
            // Get all pages from selected spaces
            const allPages = await this.getAllPages(options.selectedSpaces);
            
            this.exportState.progress.total = allPages.length;
            this.exportState.progress.completed = 0;
            this.exportState.progress.message = 'Starting export...';

            // Export pages
            await this.exportPages(allPages, options);

            // Create export package
            const exportData = await this.createExportPackage(options);

            this.exportState.isExporting = false;
            this.exportState.progress.message = 'Export completed!';
            this.exportState.status = 'completed';
            this.exportState.data = exportData;
            this.exportState.summary = {
                totalPages: allPages.length,
                totalSpaces: options.selectedSpaces.length,
                format: options.format,
                totalSize: exportData.length
            };

        } catch (error) {
            console.error('Export error:', error);
            this.exportState.isExporting = false;
            this.exportState.status = 'error';
            this.exportState.error = error.message;
        }
    }

    async getAllPages(spaceKeys) {
        const allPages = [];
        
        for (const spaceKey of spaceKeys) {
            if (this.exportState.cancelled) break;
            
            this.exportState.progress.message = `Discovering pages in space: ${spaceKey}`;
            
            try {
                let start = 0;
                const limit = 50;
                let hasMore = true;

                while (hasMore && !this.exportState.cancelled) {
                    const response = await this.makeApiRequest(
                        `/space/${spaceKey}/content?limit=${limit}&start=${start}&expand=version,space,ancestors`
                    );

                    allPages.push(...response.results);
                    
                    start += limit;
                    hasMore = response.size === limit;
                    
                    await this.rateLimitDelay();
                }
            } catch (error) {
                console.error(`Error fetching pages for space ${spaceKey}:`, error);
                // Continue with other spaces
            }
        }

        return allPages;
    }

    async exportPages(pages, options) {
        for (let i = 0; i < pages.length; i++) {
            if (this.exportState.cancelled) break;

            const page = pages[i];
            this.exportState.progress.completed = i;
            this.exportState.progress.currentPage = page.title;
            this.exportState.progress.message = `Exporting: ${page.title}`;

            try {
                const pageContent = await this.exportPage(page, options);
                this.exportedPages.push({
                    ...page,
                    exportedContent: pageContent
                });
            } catch (error) {
                console.error(`Error exporting page ${page.title}:`, error);
                // Continue with other pages
            }

            await this.rateLimitDelay();
        }

        this.exportState.progress.completed = pages.length;
    }

    async exportPage(page, options) {
        // Get full page content
        const contentResponse = await this.makeApiRequest(
            `/content/${page.id}?expand=body.storage,version,space,ancestors,children.attachment`
        );

        let content = {
            id: page.id,
            title: page.title,
            type: page.type,
            space: contentResponse.space,
            version: contentResponse.version,
            ancestors: contentResponse.ancestors || [],
            body: contentResponse.body?.storage?.value || '',
            attachments: []
        };

        // Get attachments if requested
        if (options.includeAttachments && contentResponse.children?.attachment) {
            content.attachments = await this.getPageAttachments(page.id);
        }

        return content;
    }

    async getPageAttachments(pageId) {
        try {
            const response = await this.makeApiRequest(
                `/content/${pageId}/child/attachment?expand=version,container`
            );
            return response.results || [];
        } catch (error) {
            console.error(`Error fetching attachments for page ${pageId}:`, error);
            return [];
        }
    }

    async createExportPackage(options) {
        // Create a simple JSON export for now
        // In a real implementation, you might want to create a ZIP file with HTML files
        const exportPackage = {
            metadata: {
                exportDate: new Date().toISOString(),
                format: options.format,
                includeAttachments: options.includeAttachments,
                preserveHierarchy: options.preserveHierarchy,
                totalPages: this.exportedPages.length
            },
            pages: this.exportedPages
        };

        return JSON.stringify(exportPackage, null, 2);
    }

    getExportProgress() {
        return {
            status: this.exportState.status || (this.exportState.isExporting ? 'in_progress' : 'idle'),
            ...this.exportState.progress,
            data: this.exportState.data,
            summary: this.exportState.summary,
            error: this.exportState.error
        };
    }

    cancelExport() {
        this.exportState.cancelled = true;
        this.exportState.isExporting = false;
        this.exportState.status = 'cancelled';
        this.exportState.progress.message = 'Export cancelled';
    }

    async makeApiRequest(endpoint) {
        const url = `${this.apiUrl}${endpoint}`;
        
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin'
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    async rateLimitDelay() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        const minInterval = 1000 / this.rateLimit; // milliseconds between requests

        if (timeSinceLastRequest < minInterval) {
            const delay = minInterval - timeSinceLastRequest;
            await new Promise(resolve => setTimeout(resolve, delay));
        }

        this.lastRequestTime = Date.now();
    }
}

// Initialize the exporter
const confluenceExporter = new ConfluenceExporter();
