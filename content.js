class ConfluenceExporter {
    constructor() {
        this.isConfluence = false;
        this.instanceType = '';
        this.baseUrl = '';
        this.domParser = null;
        this.exportState = {
            isExporting: false,
            cancelled: false,
            progress: {
                total: 0,
                completed: 0,
                currentPage: '',
                message: 'Initializing...'
            }
        };
        this.exportedPages = [];
        this.discoveredSpaces = [];
        this.rateLimit = 3; // requests per second

        this.init();
    }

    init() {
        console.log('🚀 ConfluenceExporter initializing...');

        // Check if ConfluenceDOMParser is available
        if (typeof ConfluenceDOMParser === 'undefined') {
            console.error('❌ ConfluenceDOMParser is not defined - waiting for it to load...');
            // Try again after a short delay
            setTimeout(() => this.init(), 100);
            return;
        }

        try {
            console.log('✅ ConfluenceDOMParser found, creating instance...');
            this.domParser = new ConfluenceDOMParser();
            this.detectConfluence();
            this.setupMessageListener();
            console.log('✅ ConfluenceExporter initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing ConfluenceExporter:', error);
        }
    }

    detectConfluence() {
        console.log('🔍 Detecting Confluence...');
        try {
            this.isConfluence = this.domParser.isConfluencePage();
            console.log('📊 Confluence detected:', this.isConfluence);

            if (this.isConfluence) {
                this.instanceType = this.domParser.getInstanceType();
                this.baseUrl = this.domParser.baseUrl;
                this.domParser.setRateLimit(this.rateLimit);
                console.log('✅ Confluence setup complete:', {
                    instanceType: this.instanceType,
                    baseUrl: this.baseUrl
                });
            } else {
                console.log('❌ Not a Confluence page');
            }
        } catch (error) {
            console.error('❌ Error detecting Confluence:', error);
            this.isConfluence = false;
        }
    }



    setupMessageListener() {
        console.log('📡 Setting up message listener...');
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            console.log('📨 Received message:', request);
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });
        console.log('✅ Message listener setup complete');
    }

    async handleMessage(request, sender, sendResponse) {
        console.log('🔧 Handling message:', request.action);
        try {
            switch (request.action) {
                case 'detectConfluence':
                    const response = {
                        isConfluence: this.isConfluence,
                        instanceType: this.instanceType,
                        baseUrl: this.baseUrl
                    };
                    console.log('📤 Sending detection response:', response);
                    sendResponse(response);
                    break;

                case 'getSpaces':
                    const spaces = await this.getSpaces();
                    sendResponse({ spaces });
                    break;

                case 'startExport':
                    this.startExport(request.options);
                    sendResponse({ status: 'started' });
                    break;

                case 'getExportProgress':
                    sendResponse(this.getExportProgress());
                    break;

                case 'cancelExport':
                    this.cancelExport();
                    sendResponse({ status: 'cancelled' });
                    break;

                default:
                    sendResponse({ error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ error: error.message });
        }
    }

    async getSpaces() {
        try {
            this.exportState.progress.message = 'Discovering spaces...';
            const spaces = await this.domParser.discoverSpaces();
            this.discoveredSpaces = spaces;

            return spaces.map(space => ({
                key: space.key,
                name: space.name,
                type: space.type || 'global',
                description: space.description || ''
            }));
        } catch (error) {
            console.error('Error discovering spaces:', error);
            throw new Error('Failed to discover spaces. Please ensure you are on a Confluence page.');
        }
    }

    async startExport(options) {
        if (this.exportState.isExporting) {
            throw new Error('Export already in progress');
        }

        this.exportState.isExporting = true;
        this.exportState.cancelled = false;
        this.exportedPages = [];
        this.rateLimit = options.rateLimit || 3;
        this.domParser.setRateLimit(this.rateLimit);

        try {
            // Get all pages from selected spaces using DOM parsing
            const allPages = await this.getAllPagesByDOMParsing(options.selectedSpaces);

            this.exportState.progress.total = allPages.length;
            this.exportState.progress.completed = 0;
            this.exportState.progress.message = 'Starting export...';

            // Export pages by navigating to each one
            await this.exportPagesByNavigation(allPages, options);

            // Create export package
            const exportData = await this.createExportPackage(options);

            this.exportState.isExporting = false;
            this.exportState.progress.message = 'Export completed!';
            this.exportState.status = 'completed';
            this.exportState.data = exportData;
            this.exportState.summary = {
                totalPages: allPages.length,
                totalSpaces: options.selectedSpaces.length,
                format: options.format,
                totalSize: exportData.length
            };

        } catch (error) {
            console.error('Export error:', error);
            this.exportState.isExporting = false;
            this.exportState.status = 'error';
            this.exportState.error = error.message;
        }
    }

    async getAllPagesByDOMParsing(spaceKeys) {
        const allPages = [];

        for (const spaceKey of spaceKeys) {
            if (this.exportState.cancelled) break;

            this.exportState.progress.message = `Discovering pages in space: ${spaceKey}`;

            try {
                const spacePages = await this.domParser.discoverSpacePages(spaceKey);
                allPages.push(...spacePages);

                console.log(`Found ${spacePages.length} pages in space ${spaceKey}`);
            } catch (error) {
                console.error(`Error discovering pages for space ${spaceKey}:`, error);
                // Continue with other spaces
            }
        }

        return allPages;
    }

    async exportPagesByNavigation(pages, options) {
        for (let i = 0; i < pages.length; i++) {
            if (this.exportState.cancelled) break;

            const page = pages[i];
            this.exportState.progress.completed = i;
            this.exportState.progress.currentPage = page.title;
            this.exportState.progress.message = `Exporting: ${page.title}`;

            try {
                const pageContent = await this.exportPageByNavigation(page, options);
                this.exportedPages.push({
                    ...page,
                    exportedContent: pageContent
                });
            } catch (error) {
                console.error(`Error exporting page ${page.title}:`, error);
                // Continue with other pages - add placeholder content
                this.exportedPages.push({
                    ...page,
                    exportedContent: {
                        body: `<p>Error loading content for page: ${page.title}</p>`,
                        error: error.message
                    }
                });
            }

            await this.domParser.rateLimitDelay();
        }

        this.exportState.progress.completed = pages.length;
    }

    async exportPageByNavigation(page, options) {
        try {
            // If this is the current page, parse it directly
            if (window.location.href === page.url ||
                window.location.href.includes(page.id)) {
                return this.domParser.parseCurrentPageContent();
            }

            // Otherwise, we need to navigate to the page
            // For security reasons, we'll use fetch to get the page content
            const response = await fetch(page.url, {
                credentials: 'same-origin',
                headers: {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const html = await response.text();
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // Extract content from the fetched document
            return this.parsePageContentFromDocument(doc, page);

        } catch (error) {
            console.error(`Error navigating to page ${page.title}:`, error);
            throw error;
        }
    }

    parsePageContentFromDocument(doc, page) {
        // Extract content using similar logic to DOM parser but from a document
        const content = {
            id: page.id,
            title: this.extractTitleFromDocument(doc),
            body: this.extractBodyFromDocument(doc),
            breadcrumbs: this.extractBreadcrumbsFromDocument(doc),
            attachments: this.extractAttachmentsFromDocument(doc),
            metadata: this.extractMetadataFromDocument(doc),
            spaceKey: page.spaceKey
        };

        return content;
    }

    extractTitleFromDocument(doc) {
        const titleSelectors = [
            '#title-text',
            '.page-title',
            'h1.title',
            '.content-title',
            '.wiki-content h1'
        ];

        for (const selector of titleSelectors) {
            const element = doc.querySelector(selector);
            if (element) {
                return element.textContent.trim();
            }
        }

        return doc.title.replace(/ - .*$/, '').trim();
    }

    extractBodyFromDocument(doc) {
        const contentSelectors = [
            '#main-content .wiki-content',
            '.wiki-content',
            '.page-content',
            '.content-body',
            '#content .main-content'
        ];

        for (const selector of contentSelectors) {
            const element = doc.querySelector(selector);
            if (element) {
                return this.cleanupContentFromDocument(element.innerHTML);
            }
        }

        return '';
    }

    cleanupContentFromDocument(html) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        const unwantedSelectors = [
            'script',
            'style',
            '.edit-button',
            '.page-metadata',
            '.confluence-information-macro-information'
        ];

        unwantedSelectors.forEach(selector => {
            const elements = tempDiv.querySelectorAll(selector);
            elements.forEach(el => el.remove());
        });

        return tempDiv.innerHTML;
    }

    extractBreadcrumbsFromDocument(doc) {
        const breadcrumbs = [];
        const breadcrumbSelectors = [
            '.aui-nav-breadcrumbs a',
            '.breadcrumbs a',
            '.page-breadcrumbs a'
        ];

        for (const selector of breadcrumbSelectors) {
            const links = doc.querySelectorAll(selector);
            if (links.length > 0) {
                links.forEach(link => {
                    breadcrumbs.push({
                        title: link.textContent.trim(),
                        url: link.href
                    });
                });
                break;
            }
        }

        return breadcrumbs;
    }

    extractAttachmentsFromDocument(doc) {
        const attachments = [];
        const attachmentSelectors = [
            '.attachment-list a',
            '.attachments a',
            'a[href*="/download/attachments/"]'
        ];

        attachmentSelectors.forEach(selector => {
            const links = doc.querySelectorAll(selector);
            links.forEach(link => {
                attachments.push({
                    title: link.textContent.trim(),
                    url: link.href,
                    type: this.getFileTypeFromUrl(link.href)
                });
            });
        });

        return attachments;
    }

    extractMetadataFromDocument(doc) {
        return {
            lastModified: this.extractLastModifiedFromDocument(doc),
            author: this.extractAuthorFromDocument(doc),
            version: this.extractVersionFromDocument(doc),
            labels: this.extractLabelsFromDocument(doc)
        };
    }

    extractLastModifiedFromDocument(doc) {
        const metaSelectors = [
            '.last-modified time',
            '.page-metadata time',
            '[data-last-modified]'
        ];

        for (const selector of metaSelectors) {
            const element = doc.querySelector(selector);
            if (element) {
                return element.getAttribute('datetime') || element.textContent.trim();
            }
        }

        return null;
    }

    extractAuthorFromDocument(doc) {
        const authorSelectors = [
            '.author a',
            '.page-metadata .author',
            '[data-author]'
        ];

        for (const selector of authorSelectors) {
            const element = doc.querySelector(selector);
            if (element) {
                return element.textContent.trim();
            }
        }

        return null;
    }

    extractVersionFromDocument(doc) {
        const versionSelectors = [
            '.version-number',
            '[data-version]'
        ];

        for (const selector of versionSelectors) {
            const element = doc.querySelector(selector);
            if (element) {
                return element.textContent.trim();
            }
        }

        return null;
    }

    extractLabelsFromDocument(doc) {
        const labels = [];
        const labelSelectors = [
            '.labels a',
            '.page-labels a',
            '.content-labels a'
        ];

        labelSelectors.forEach(selector => {
            const links = doc.querySelectorAll(selector);
            links.forEach(link => {
                labels.push(link.textContent.trim());
            });
        });

        return labels;
    }

    getFileTypeFromUrl(url) {
        const extension = url.split('.').pop().toLowerCase();
        const typeMap = {
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'svg': 'image/svg+xml'
        };

        return typeMap[extension] || 'application/octet-stream';
    }

    async createExportPackage(options) {
        // Use the export utils to create the appropriate format
        const exportUtils = new ExportUtils();

        if (options.format === 'html') {
            const htmlExport = await exportUtils.createHtmlExport(this.exportedPages, options);
            return JSON.stringify(htmlExport, null, 2);
        } else {
            return exportUtils.createJsonExport(this.exportedPages, options);
        }
    }

    getExportProgress() {
        return {
            status: this.exportState.status || (this.exportState.isExporting ? 'in_progress' : 'idle'),
            ...this.exportState.progress,
            data: this.exportState.data,
            summary: this.exportState.summary,
            error: this.exportState.error
        };
    }

    cancelExport() {
        this.exportState.cancelled = true;
        this.exportState.isExporting = false;
        this.exportState.status = 'cancelled';
        this.exportState.progress.message = 'Export cancelled';
    }


}

// Initialize the exporter with error handling
console.log('🌟 Content script loading...');

try {
    const confluenceExporter = new ConfluenceExporter();

    // Make it globally accessible for debugging
    window.confluenceExporter = confluenceExporter;

    console.log('🎉 Content script loaded successfully');
} catch (error) {
    console.error('💥 Fatal error loading content script:', error);

    // Send error to background script
    chrome.runtime.sendMessage({
        action: 'contentScriptError',
        error: error.message,
        stack: error.stack
    }).catch(err => console.error('Failed to send error message:', err));
}
