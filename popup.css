* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
}

.container {
    width: 400px;
    max-height: 600px;
    overflow-y: auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #0052cc, #0065ff);
    color: white;
    border-radius: 8px 8px 0 0;
}

.logo {
    width: 24px;
    height: 24px;
    margin-right: 12px;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
}

.content {
    padding: 20px;
}

.status-section {
    margin-bottom: 20px;
}

.status-indicator {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f1f3f4;
    border-radius: 6px;
    border-left: 4px solid #ffa500;
}

.status-indicator.success {
    background: #e8f5e8;
    border-left-color: #28a745;
}

.status-indicator.error {
    background: #ffeaea;
    border-left-color: #dc3545;
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ffa500;
    margin-right: 8px;
    animation: pulse 2s infinite;
}

.status-indicator.success .dot {
    background: #28a745;
    animation: none;
}

.status-indicator.error .dot {
    background: #dc3545;
    animation: none;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.options-section, .space-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
}

.options-section h3, .space-section h3 {
    margin-bottom: 12px;
    font-size: 16px;
    color: #333;
}

.option-group {
    margin-bottom: 12px;
}

.option-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
}

.option-group input[type="checkbox"] {
    margin-right: 8px;
}

.option-group select, .option-group input[type="range"] {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.option-group input[type="range"] {
    width: calc(100% - 30px);
    margin-right: 8px;
}

#rate-limit-value {
    font-weight: 600;
    color: #0052cc;
}

.space-list {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    margin-bottom: 12px;
}

.space-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
}

.space-item:last-child {
    border-bottom: none;
}

.space-item input[type="checkbox"] {
    margin-right: 8px;
}

.space-info {
    flex: 1;
}

.space-name {
    font-weight: 500;
    color: #333;
}

.space-key {
    font-size: 12px;
    color: #666;
}

.space-actions {
    display: flex;
    gap: 8px;
}

.btn {
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #0052cc;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0065ff;
}

.btn-secondary {
    background: #f1f3f4;
    color: #333;
    border: 1px solid #ddd;
}

.btn-secondary:hover:not(:disabled) {
    background: #e8eaed;
}

.btn-cancel {
    background: #dc3545;
    color: white;
    font-size: 12px;
    padding: 6px 12px;
}

.btn-cancel:hover {
    background: #c82333;
}

.action-section {
    text-align: center;
    margin-bottom: 20px;
}

.progress-section {
    margin-bottom: 20px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.progress-header h3 {
    font-size: 16px;
    color: #333;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0052cc, #0065ff);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 13px;
}

.current-page {
    font-size: 12px;
    color: #666;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    word-break: break-word;
}

.results-section {
    text-align: center;
    margin-bottom: 20px;
}

.results-header h3 {
    color: #28a745;
    margin-bottom: 12px;
}

.results-content {
    padding: 16px;
    background: #e8f5e8;
    border-radius: 6px;
}

.results-summary {
    margin-bottom: 16px;
    font-size: 14px;
    color: #333;
}

.results-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.error-section {
    margin-bottom: 20px;
}

.error-content {
    padding: 16px;
    background: #ffeaea;
    border-radius: 6px;
    text-align: center;
}

.error-content h3 {
    color: #dc3545;
    margin-bottom: 8px;
}

.error-content p {
    margin-bottom: 12px;
    color: #333;
}

.footer {
    padding: 12px 20px;
    background: #f8f9fa;
    border-top: 1px solid #eee;
    border-radius: 0 0 8px 8px;
}

.help-text {
    text-align: center;
    color: #666;
}

/* Scrollbar styling */
.container::-webkit-scrollbar,
.space-list::-webkit-scrollbar {
    width: 6px;
}

.container::-webkit-scrollbar-track,
.space-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.container::-webkit-scrollbar-thumb,
.space-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.container::-webkit-scrollbar-thumb:hover,
.space-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
