# Icons Directory

This directory should contain the following icon files for the Chrome extension:

## Required Icons:
- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

## Optional Icons (for inactive state):
- `icon16-gray.png` - 16x16 pixels (grayed out when not on Confluence)
- `icon48-gray.png` - 48x48 pixels (grayed out)
- `icon128-gray.png` - 128x128 pixels (grayed out)

## Icon Design Guidelines:
- Use a document/export theme (📚, 📥, 📄)
- Primary color: #0052cc (Confluence blue)
- Background: Transparent or white
- Simple, recognizable design that works at small sizes

## Creating Icons:
You can create these icons using:
1. Any image editor (GIMP, Photoshop, Figma, etc.)
2. Online icon generators
3. Convert from SVG using tools like ImageMagick

## Temporary Placeholder:
For testing purposes, you can use any 16x16, 48x48, and 128x128 PNG files.
The extension will work without custom icons, but Chrome will show default placeholder icons.
