// Debug script to run in browser console on Confluence page
// Copy and paste this into the browser console (F12) on izberg.atlassian.net

console.log('🔧 Starting Confluence Extension Debug...');

// 1. Check basic page info
console.log('📍 Current URL:', window.location.href);
console.log('🌐 Hostname:', window.location.hostname);
console.log('📄 Page Title:', document.title);

// 2. Check if content scripts are loaded
console.log('🔍 Checking content script loading...');
console.log('ConfluenceExporter available:', typeof window.confluenceExporter !== 'undefined');
console.log('ConfluenceDOMParser available:', typeof ConfluenceDOMParser !== 'undefined');

// 3. Check Confluence indicators
console.log('🔍 Checking Confluence indicators...');

const indicators = {
    'Meta confluence-base-url': document.querySelector('meta[name="confluence-base-url"]'),
    'Meta ajs-base-url': document.querySelector('meta[name="ajs-base-url"]'),
    'Meta application-name': document.querySelector('meta[name="application-name"]'),
    'Confluence navigation': document.querySelector('#confluence-navigation'),
    'Confluence content': document.querySelector('.confluence-content'),
    'Wiki content': document.querySelector('#main-content.wiki-content'),
    'Test ID confluence-navigation': document.querySelector('[data-testid="confluence-navigation"]'),
    'CSS emotion classes': document.querySelector('[class*="css-"]'),
    'AJS object': typeof window.AJS !== 'undefined',
    'WRM object': typeof window.WRM !== 'undefined',
    'URL contains /wiki/': window.location.href.includes('/wiki/'),
    'Title contains Confluence': document.title.includes('Confluence'),
    'Body has confluence class': document.body.classList.contains('confluence'),
    'Atlassian domain': window.location.hostname.includes('atlassian.net')
};

Object.entries(indicators).forEach(([name, value]) => {
    if (value) {
        console.log(`✅ ${name}:`, value);
    } else {
        console.log(`❌ ${name}: Not found`);
    }
});

// 4. Check extension communication
console.log('📡 Testing extension communication...');

if (typeof chrome !== 'undefined' && chrome.runtime) {
    // Test if we can communicate with the extension
    chrome.runtime.sendMessage({action: 'ping'}, (response) => {
        if (chrome.runtime.lastError) {
            console.error('❌ Extension communication error:', chrome.runtime.lastError);
        } else {
            console.log('✅ Extension communication working:', response);
        }
    });
} else {
    console.error('❌ Chrome extension API not available');
}

// 5. Manual DOM parser test
console.log('🧪 Testing DOM parser manually...');

if (typeof ConfluenceDOMParser !== 'undefined') {
    try {
        const parser = new ConfluenceDOMParser();
        const isConfluence = parser.isConfluencePage();
        const instanceType = parser.getInstanceType();
        
        console.log('✅ Manual DOM parser test results:');
        console.log('  - Is Confluence:', isConfluence);
        console.log('  - Instance Type:', instanceType);
        console.log('  - Base URL:', parser.baseUrl);
    } catch (error) {
        console.error('❌ DOM parser error:', error);
    }
} else {
    console.error('❌ ConfluenceDOMParser not loaded');
}

// 6. Check for common issues
console.log('🔍 Checking for common issues...');

// Check if page is fully loaded
if (document.readyState === 'complete') {
    console.log('✅ Page fully loaded');
} else {
    console.log('⚠️ Page still loading, state:', document.readyState);
}

// Check for CSP issues
const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
if (cspMeta) {
    console.log('⚠️ CSP detected:', cspMeta.content);
} else {
    console.log('✅ No CSP meta tag found');
}

// Check for iframe context
if (window !== window.top) {
    console.log('⚠️ Running in iframe context');
} else {
    console.log('✅ Running in main window context');
}

// 7. Extension manifest check
console.log('📋 Extension manifest info...');
if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest) {
    const manifest = chrome.runtime.getManifest();
    console.log('Extension version:', manifest.version);
    console.log('Content script matches:', manifest.content_scripts[0].matches);
} else {
    console.log('❌ Cannot access extension manifest');
}

// 8. Provide debugging summary
console.log('📊 Debug Summary:');
console.log('================');

const debugInfo = {
    url: window.location.href,
    hostname: window.location.hostname,
    title: document.title,
    readyState: document.readyState,
    contentScriptLoaded: typeof window.confluenceExporter !== 'undefined',
    domParserLoaded: typeof ConfluenceDOMParser !== 'undefined',
    chromeApiAvailable: typeof chrome !== 'undefined',
    atlassianDomain: window.location.hostname.includes('atlassian.net'),
    wikiPath: window.location.href.includes('/wiki/'),
    confluenceInTitle: document.title.includes('Confluence')
};

console.table(debugInfo);

// 9. Recommendations
console.log('💡 Recommendations:');
if (!debugInfo.contentScriptLoaded) {
    console.log('❌ Content script not loaded - reload extension and refresh page');
}
if (!debugInfo.atlassianDomain) {
    console.log('❌ Not on Atlassian domain - navigate to izberg.atlassian.net');
}
if (!debugInfo.wikiPath) {
    console.log('⚠️ Not on /wiki/ path - try navigating to a wiki page');
}
if (debugInfo.readyState !== 'complete') {
    console.log('⚠️ Page not fully loaded - wait for page to finish loading');
}

console.log('🔧 Debug complete! Check the logs above for issues.');
