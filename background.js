// Background service worker for Confluence Exporter
class ConfluenceExporterBackground {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Handle extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstallation(details);
        });

        // Handle messages from popup and content scripts
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Handle tab updates to detect Confluence pages
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });

        // Handle download events
        chrome.downloads.onChanged.addListener((downloadDelta) => {
            this.handleDownloadChange(downloadDelta);
        });
    }

    handleInstallation(details) {
        if (details.reason === 'install') {
            console.log('Confluence Exporter installed');
            
            // Set default settings
            chrome.storage.sync.set({
                exportSettings: {
                    format: 'html',
                    includeAttachments: true,
                    preserveHierarchy: true,
                    rateLimit: 3
                }
            });

            // Show welcome page or instructions
            chrome.tabs.create({
                url: chrome.runtime.getURL('welcome.html')
            });
        } else if (details.reason === 'update') {
            console.log('Confluence Exporter updated');
        }
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'checkConfluencePage':
                    const isConfluence = await this.checkIfConfluencePage(sender.tab);
                    sendResponse({ isConfluence });
                    break;

                case 'downloadExport':
                    const downloadId = await this.initiateDownload(request.data);
                    sendResponse({ downloadId });
                    break;

                case 'getStoredData':
                    const data = await this.getStoredData(request.key);
                    sendResponse({ data });
                    break;

                case 'storeData':
                    await this.storeData(request.key, request.data);
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling background message:', error);
            sendResponse({ error: error.message });
        }
    }

    handleTabUpdate(tabId, changeInfo, tab) {
        // Update extension icon based on whether the page is Confluence
        if (changeInfo.status === 'complete' && tab.url) {
            this.updateExtensionIcon(tabId, tab.url);
        }
    }

    handleDownloadChange(downloadDelta) {
        if (downloadDelta.state && downloadDelta.state.current === 'complete') {
            console.log('Export download completed:', downloadDelta.id);
            
            // Notify user of successful download
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'Confluence Export Complete',
                message: 'Your Confluence documentation has been exported successfully.'
            });
        } else if (downloadDelta.state && downloadDelta.state.current === 'interrupted') {
            console.error('Export download failed:', downloadDelta.id);
            
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'Export Download Failed',
                message: 'There was an error downloading your export. Please try again.'
            });
        }
    }

    async checkIfConfluencePage(tab) {
        if (!tab || !tab.url) return false;

        // Basic URL pattern matching for Confluence
        const confluencePatterns = [
            /\/wiki\//,
            /\/confluence\//,
            /\.atlassian\.net/,
            /\/display\//,
            /\/pages\//
        ];

        return confluencePatterns.some(pattern => pattern.test(tab.url));
    }

    async updateExtensionIcon(tabId, url) {
        const isConfluence = await this.checkIfConfluencePage({ url });
        
        if (isConfluence) {
            // Set active icon
            chrome.action.setIcon({
                tabId: tabId,
                path: {
                    16: 'icons/icon16.png',
                    48: 'icons/icon48.png',
                    128: 'icons/icon128.png'
                }
            });
            
            chrome.action.setTitle({
                tabId: tabId,
                title: 'Export Confluence Documentation'
            });
        } else {
            // Set inactive/grayed out icon
            chrome.action.setIcon({
                tabId: tabId,
                path: {
                    16: 'icons/icon16-gray.png',
                    48: 'icons/icon48-gray.png',
                    128: 'icons/icon128-gray.png'
                }
            });
            
            chrome.action.setTitle({
                tabId: tabId,
                title: 'Confluence Exporter (Navigate to Confluence to use)'
            });
        }
    }

    async initiateDownload(exportData) {
        try {
            // Create blob URL for the export data
            const blob = new Blob([exportData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            // Generate filename with timestamp
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `confluence-export-${timestamp}.json`;
            
            // Start download
            const downloadId = await chrome.downloads.download({
                url: url,
                filename: filename,
                saveAs: true
            });

            // Clean up blob URL after a delay
            setTimeout(() => {
                URL.revokeObjectURL(url);
            }, 10000);

            return downloadId;
        } catch (error) {
            console.error('Error initiating download:', error);
            throw error;
        }
    }

    async getStoredData(key) {
        return new Promise((resolve) => {
            chrome.storage.local.get([key], (result) => {
                resolve(result[key]);
            });
        });
    }

    async storeData(key, data) {
        return new Promise((resolve) => {
            chrome.storage.local.set({ [key]: data }, () => {
                resolve();
            });
        });
    }

    // Utility method to inject content script if needed
    async injectContentScript(tabId) {
        try {
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content.js', 'confluence-api.js', 'export-utils.js']
            });
        } catch (error) {
            console.error('Error injecting content script:', error);
        }
    }

    // Method to handle large export data that might exceed message size limits
    async handleLargeExport(exportData) {
        // Store large export data in local storage temporarily
        const exportId = 'export_' + Date.now();
        await this.storeData(exportId, exportData);
        
        // Return reference to stored data
        return { exportId, size: exportData.length };
    }

    // Clean up old export data from storage
    async cleanupOldExports() {
        const storage = await chrome.storage.local.get();
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24 hours

        for (const key in storage) {
            if (key.startsWith('export_')) {
                const timestamp = parseInt(key.split('_')[1]);
                if (now - timestamp > maxAge) {
                    chrome.storage.local.remove(key);
                }
            }
        }
    }
}

// Initialize background service worker
const confluenceExporterBackground = new ConfluenceExporterBackground();

// Clean up old exports periodically
setInterval(() => {
    confluenceExporterBackground.cleanupOldExports();
}, 60 * 60 * 1000); // Every hour
