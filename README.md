# Confluence Documentation Exporter

A Chrome extension that exports Confluence documentation by parsing web pages directly, designed specifically for izberg.atlassian.net and other Confluence Cloud instances.

## 🚀 Features

- **DOM-Based Parsing**: Works without API permissions by parsing web content directly
- **Confluence Cloud Optimized**: Specifically designed for izberg.atlassian.net
- **Auto-Discovery**: Automatically detects Confluence instances and discovers accessible pages through web navigation
- **Bulk Export**: Export all pages from selected spaces with a single click
- **Progress Tracking**: Real-time progress indicator with current page status
- **Rate Limiting**: Configurable request throttling to avoid overwhelming servers
- **HTML Export**: Clean HTML export with navigation and preserved formatting
- **Hierarchy Preservation**: Maintains page structure and relationships
- **Attachment Detection**: Identifies and includes attachment references
- **User-Friendly**: Simple popup interface with clear status indicators

## 📋 Requirements

- Chrome browser (Manifest V3 compatible)
- Access to izberg.atlassian.net or other Confluence Cloud instance
- Valid user session with web access to desired spaces
- **No API permissions required** - works through web interface parsing

## 🛠️ Installation

### Option 1: Load as Unpacked Extension (Development)

1. **Download/Clone** this repository to your local machine
2. **Open Chrome** and navigate to `chrome://extensions/`
3. **Enable Developer Mode** (toggle in top-right corner)
4. **Click "Load unpacked"** and select the extension directory
5. **Add Icons** (see Icons section below)
6. The extension should now appear in your Chrome toolbar

### Option 2: Chrome Web Store (Coming Soon)

The extension will be available on the Chrome Web Store once published.

## 🎨 Icons Setup

Before using the extension, you need to add icon files to the `icons/` directory:

**Required files:**
- `icon16.png` (16x16 pixels)
- `icon48.png` (48x48 pixels) 
- `icon128.png` (128x128 pixels)

**Quick Setup:**
```bash
# Create simple placeholder icons (requires ImageMagick)
convert -size 16x16 xc:blue icons/icon16.png
convert -size 48x48 xc:blue icons/icon48.png
convert -size 128x128 xc:blue icons/icon128.png
```

Or use any PNG files of the correct dimensions for testing.

## 📖 Usage

### Step 1: Navigate to Confluence
1. Open any page on izberg.atlassian.net
2. The extension icon should become active (colored)

### Step 2: Open Extension
1. Click the extension icon in the Chrome toolbar
2. The popup will automatically detect the Confluence instance

### Step 3: Configure Export
1. **Select Spaces**: Choose which spaces to export (all discovered spaces selected by default)
2. **Export Format**: Choose HTML (recommended) or JSON
3. **Options**:
   - ✅ Include attachment references
   - ✅ Preserve page hierarchy
   - ⚙️ Rate limit: 1-10 requests/second (default: 3) - **Important for web scraping**

### Step 4: Start Export
1. Click **"Export All Pages"**
2. Monitor progress in real-time
3. Cancel anytime if needed

### Step 5: Download Results
1. When complete, click **"Download Export"**
2. Choose save location
3. Extract and view your documentation

## 📁 Export Formats

### HTML Export (Recommended)
- **Structure**:
  ```
  confluence-export-YYYY-MM-DD/
  ├── index.html              # Main navigation page
  ├── pages/                  # Individual page files
  │   ├── page-title-1.html
  │   └── page-title-2.html
  ├── styles/                 # CSS styling
  │   └── confluence-export.css
  └── attachments/            # Attachment references
      └── attachment-links.html
  ```
- **Features**: Clickable navigation, preserved formatting, offline viewing
- **Best for**: Documentation websites, offline reference
- **Note**: Attachments are referenced but not downloaded due to security restrictions

### JSON Export
- **Structure**: Single JSON file with all page data and metadata
- **Features**: Machine-readable, preserves metadata, hierarchical structure, attachment references
- **Best for**: Data analysis, migration, custom processing

## ⚙️ Configuration

### Rate Limiting
- **Purpose**: Prevents overwhelming Confluence servers and avoids being blocked
- **Range**: 1-10 requests per second
- **Recommendation**:
  - izberg.atlassian.net: 2-3 req/sec (recommended)
  - Other Cloud instances: 1-3 req/sec
  - Large exports: Start with 1-2 req/sec
- **Important**: Too aggressive scraping may trigger rate limiting or IP blocking

### Space Selection
- **Default**: All accessible spaces selected
- **Filtering**: Uncheck spaces you don't need
- **Permissions**: Only spaces you can read will be available

## 🔧 Technical Details

### Architecture
- **Manifest V3**: Modern Chrome extension standard
- **Content Scripts**: Interact with Confluence pages via DOM parsing
- **Background Service Worker**: Coordinate export process
- **Popup Interface**: User interaction and progress display

### DOM Parsing Approach
- **Web Scraping**: Parses Confluence pages directly from HTML
- **Authentication**: Uses existing browser session
- **Methods Used**:
  - Space discovery through navigation parsing
  - Page discovery through space content parsing
  - Content extraction from page DOM
  - Attachment reference extraction

### Browser Permissions
- `activeTab`: Access current Confluence page
- `storage`: Save user preferences
- `downloads`: Save export files
- `host_permissions`: Access any website (for Confluence instances)

## 🐛 Troubleshooting

### Extension Not Working
1. **Check Confluence Detection**:
   - Ensure you're on a Confluence page
   - Look for "✅ Confluence detected" in popup
   
2. **Refresh and Retry**:
   - Refresh the Confluence page
   - Reopen the extension popup

### Export Fails
1. **Check Permissions**:
   - Ensure you can access the spaces manually
   - Try logging out and back into Confluence

2. **Reduce Rate Limit**:
   - Lower requests/second to 1-2
   - Some servers have strict rate limiting

3. **Check Network**:
   - Ensure stable internet connection
   - Corporate firewalls may block requests

### Large Exports
1. **Memory Issues**:
   - Export smaller space subsets
   - Close other browser tabs
   - Restart browser if needed

2. **Timeout Issues**:
   - Reduce rate limit
   - Export spaces individually

## 🔒 Privacy & Security

- **No Data Collection**: Extension doesn't collect or transmit personal data
- **Local Processing**: All export processing happens locally
- **Session-Based**: Uses your existing Confluence authentication
- **No External Servers**: Direct communication with your Confluence instance only

## 🤝 Contributing

### Development Setup
```bash
git clone <repository-url>
cd confluence-exporter
# Load as unpacked extension in Chrome
```

### File Structure
```
confluence-exporter/
├── manifest.json           # Extension configuration
├── popup.html              # Main UI
├── popup.css               # UI styling
├── popup.js                # UI logic
├── content.js              # Page interaction
├── background.js           # Service worker
├── confluence-api.js       # API utilities
├── export-utils.js         # Export processing
├── icons/                  # Extension icons
└── README.md               # This file
```

### Key Components
- **popup.js**: Main UI controller and user interaction
- **content.js**: Confluence detection and export orchestration
- **confluence-api.js**: REST API communication and rate limiting
- **export-utils.js**: Content processing and format generation

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues, questions, or feature requests:
1. Check the troubleshooting section above
2. Create an issue in the repository
3. Include browser version, Confluence type, and error details

## 🔄 Version History

### v1.0.0 (Current)
- Initial release
- HTML and JSON export formats
- Confluence Cloud and Server support
- Rate limiting and progress tracking
- Attachment support
- Hierarchical structure preservation

### Planned Features
- PDF export format
- Advanced filtering options
- Bulk space management
- Export scheduling
- Custom styling options
