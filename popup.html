<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confluence Exporter</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="icons/icon48.png" alt="Confluence Exporter" class="logo">
            <h1>Confluence Exporter</h1>
        </div>
        
        <div class="content">
            <div id="detection-status" class="status-section">
                <div class="status-indicator" id="confluence-indicator">
                    <span class="dot"></span>
                    <span id="confluence-status">Detecting Confluence...</span>
                </div>
            </div>

            <div id="export-options" class="options-section" style="display: none;">
                <h3>Export Options</h3>
                
                <div class="option-group">
                    <label for="export-format">Export Format:</label>
                    <select id="export-format">
                        <option value="html">HTML (Recommended)</option>
                        <option value="pdf" disabled>PDF (Coming Soon)</option>
                    </select>
                </div>

                <div class="option-group">
                    <label>
                        <input type="checkbox" id="include-attachments" checked>
                        Include attachments and images
                    </label>
                </div>

                <div class="option-group">
                    <label>
                        <input type="checkbox" id="preserve-hierarchy" checked>
                        Preserve page hierarchy
                    </label>
                </div>

                <div class="option-group">
                    <label for="rate-limit">Rate Limit (requests/second):</label>
                    <input type="range" id="rate-limit" min="1" max="10" value="3">
                    <span id="rate-limit-value">3</span>
                </div>
            </div>

            <div id="space-selection" class="space-section" style="display: none;">
                <h3>Select Spaces to Export</h3>
                <div class="space-list" id="space-list">
                    <!-- Spaces will be populated dynamically -->
                </div>
                <div class="space-actions">
                    <button id="select-all-spaces" class="btn btn-secondary">Select All</button>
                    <button id="deselect-all-spaces" class="btn btn-secondary">Deselect All</button>
                </div>
            </div>

            <div class="action-section">
                <button id="export-btn" class="btn btn-primary" disabled>
                    <span class="btn-text">Export All Pages</span>
                    <span class="btn-icon">📥</span>
                </button>
            </div>

            <div id="progress-section" class="progress-section" style="display: none;">
                <div class="progress-header">
                    <h3>Export Progress</h3>
                    <button id="cancel-export" class="btn btn-cancel">Cancel</button>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                
                <div class="progress-info">
                    <div id="progress-text">Preparing export...</div>
                    <div id="progress-stats">0 / 0 pages</div>
                </div>
                
                <div class="current-page">
                    <strong>Current:</strong> <span id="current-page-title">-</span>
                </div>
            </div>

            <div id="results-section" class="results-section" style="display: none;">
                <div class="results-header">
                    <h3 id="results-title">Export Complete!</h3>
                </div>
                <div class="results-content">
                    <div id="results-summary"></div>
                    <div class="results-actions">
                        <button id="download-export" class="btn btn-primary">Download Export</button>
                        <button id="new-export" class="btn btn-secondary">Start New Export</button>
                    </div>
                </div>
            </div>

            <div id="error-section" class="error-section" style="display: none;">
                <div class="error-content">
                    <h3>⚠️ Error</h3>
                    <p id="error-message"></p>
                    <button id="retry-btn" class="btn btn-secondary">Retry</button>
                </div>
            </div>
        </div>

        <div class="footer">
            <div class="help-text">
                <small>This extension exports all Confluence pages you have access to.</small>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
