#!/bin/bash

# Create placeholder icons for Confluence Exporter Chrome Extension
# This script creates simple colored square icons as placeholders

echo "Creating placeholder icons for Confluence Exporter..."

# Create icons directory if it doesn't exist
mkdir -p icons

# Check if ImageMagick is available
if command -v convert &> /dev/null; then
    echo "Using ImageMagick to create icons..."
    
    # Create blue square icons with white "C" text
    convert -size 16x16 xc:"#0052cc" -fill white -gravity center -pointsize 12 -annotate +0+0 "C" icons/icon16.png
    convert -size 48x48 xc:"#0052cc" -fill white -gravity center -pointsize 36 -annotate +0+0 "C" icons/icon48.png
    convert -size 128x128 xc:"#0052cc" -fill white -gravity center -pointsize 96 -annotate +0+0 "C" icons/icon128.png
    
    echo "✅ Icons created successfully with ImageMagick!"
    
elif command -v curl &> /dev/null; then
    echo "Using online placeholder service to create icons..."
    
    # Download placeholder icons from placeholder service
    curl -s -o icons/icon16.png "https://via.placeholder.com/16x16/0052cc/ffffff?text=C"
    curl -s -o icons/icon48.png "https://via.placeholder.com/48x48/0052cc/ffffff?text=C"
    curl -s -o icons/icon128.png "https://via.placeholder.com/128x128/0052cc/ffffff?text=C"
    
    echo "✅ Icons downloaded successfully!"
    
else
    echo "❌ Neither ImageMagick nor curl is available."
    echo "Please install one of them or create icons manually:"
    echo ""
    echo "Manual creation:"
    echo "1. Create 16x16, 48x48, and 128x128 pixel PNG images"
    echo "2. Save them as icons/icon16.png, icons/icon48.png, icons/icon128.png"
    echo "3. Use any image editor or online icon generator"
    echo ""
    echo "Alternative installation:"
    echo "- Ubuntu/Debian: sudo apt-get install imagemagick"
    echo "- macOS: brew install imagemagick"
    echo "- Windows: Download from https://imagemagick.org/"
    exit 1
fi

# Verify icons were created
echo ""
echo "Verifying created icons:"
for size in 16 48 128; do
    if [ -f "icons/icon${size}.png" ]; then
        file_size=$(ls -lh "icons/icon${size}.png" | awk '{print $5}')
        echo "✅ icon${size}.png - ${file_size}"
    else
        echo "❌ icon${size}.png - Missing!"
    fi
done

echo ""
echo "🎉 Icon creation complete!"
echo ""
echo "Next steps:"
echo "1. Load the extension in Chrome (chrome://extensions/)"
echo "2. Enable Developer Mode"
echo "3. Click 'Load unpacked' and select this directory"
echo "4. Navigate to a Confluence page to test"
