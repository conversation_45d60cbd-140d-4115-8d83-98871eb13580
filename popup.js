class ConfluenceExporterPopup {
    constructor() {
        this.isExporting = false;
        this.exportData = null;
        this.selectedSpaces = new Set();
        this.init();
    }

    async init() {
        this.bindEvents();
        await this.detectConfluence();
        this.loadSettings();
    }

    bindEvents() {
        // Rate limit slider
        const rateLimitSlider = document.getElementById('rate-limit');
        const rateLimitValue = document.getElementById('rate-limit-value');
        rateLimitSlider.addEventListener('input', (e) => {
            rateLimitValue.textContent = e.target.value;
        });

        // Space selection
        document.getElementById('select-all-spaces').addEventListener('click', () => {
            this.selectAllSpaces(true);
        });

        document.getElementById('deselect-all-spaces').addEventListener('click', () => {
            this.selectAllSpaces(false);
        });

        // Export button
        document.getElementById('export-btn').addEventListener('click', () => {
            this.startExport();
        });

        // Cancel export
        document.getElementById('cancel-export').addEventListener('click', () => {
            this.cancelExport();
        });

        // Results actions
        document.getElementById('download-export').addEventListener('click', () => {
            this.downloadExport();
        });

        document.getElementById('new-export').addEventListener('click', () => {
            this.resetToInitialState();
        });

        // Retry button
        document.getElementById('retry-btn').addEventListener('click', () => {
            this.resetToInitialState();
            this.detectConfluence();
        });
    }

    async detectConfluence() {
        const statusElement = document.getElementById('confluence-status');
        const indicator = document.getElementById('confluence-indicator');
        
        try {
            // Get current tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            // Send message to content script to detect Confluence
            const response = await chrome.tabs.sendMessage(tab.id, { 
                action: 'detectConfluence' 
            });

            if (response && response.isConfluence) {
                statusElement.textContent = `✅ Confluence detected: ${response.instanceType}`;
                indicator.classList.add('success');

                // Load spaces
                await this.loadSpaces(tab.id);

                // Show export options
                document.getElementById('export-options').style.display = 'block';
                document.getElementById('space-selection').style.display = 'block';
                document.getElementById('export-btn').disabled = false;
            } else {
                statusElement.textContent = '❌ No Confluence instance detected';
                indicator.classList.add('error');
                this.showError('This page does not appear to be a Confluence instance. Please navigate to izberg.atlassian.net and try again.');
            }
        } catch (error) {
            console.error('Error detecting Confluence:', error);
            statusElement.textContent = '❌ Error detecting Confluence';
            indicator.classList.add('error');
            this.showError('Unable to communicate with the page. Please ensure you are on izberg.atlassian.net and refresh the page.');
        }
    }

    async loadSpaces(tabId) {
        try {
            const response = await chrome.tabs.sendMessage(tabId, { 
                action: 'getSpaces' 
            });

            if (response && response.spaces) {
                this.renderSpaces(response.spaces);
            } else {
                throw new Error('No spaces found');
            }
        } catch (error) {
            console.error('Error loading spaces:', error);
            this.showError('Unable to discover Confluence spaces. Please ensure you are on a Confluence page with space navigation visible.');
        }
    }

    renderSpaces(spaces) {
        const spaceList = document.getElementById('space-list');
        spaceList.innerHTML = '';

        spaces.forEach(space => {
            const spaceItem = document.createElement('div');
            spaceItem.className = 'space-item';
            
            spaceItem.innerHTML = `
                <input type="checkbox" id="space-${space.key}" value="${space.key}" checked>
                <div class="space-info">
                    <div class="space-name">${space.name}</div>
                    <div class="space-key">${space.key}</div>
                </div>
            `;

            const checkbox = spaceItem.querySelector('input[type="checkbox"]');
            checkbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.selectedSpaces.add(space.key);
                } else {
                    this.selectedSpaces.delete(space.key);
                }
            });

            // Initially select all spaces
            this.selectedSpaces.add(space.key);
            spaceList.appendChild(spaceItem);
        });
    }

    selectAllSpaces(select) {
        const checkboxes = document.querySelectorAll('#space-list input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = select;
            if (select) {
                this.selectedSpaces.add(checkbox.value);
            } else {
                this.selectedSpaces.delete(checkbox.value);
            }
        });
    }

    async startExport() {
        if (this.isExporting) return;

        this.isExporting = true;
        this.showProgressSection();

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const exportOptions = {
                format: document.getElementById('export-format').value,
                includeAttachments: document.getElementById('include-attachments').checked,
                preserveHierarchy: document.getElementById('preserve-hierarchy').checked,
                rateLimit: parseInt(document.getElementById('rate-limit').value),
                selectedSpaces: Array.from(this.selectedSpaces)
            };

            // Start export process
            await chrome.tabs.sendMessage(tab.id, {
                action: 'startExport',
                options: exportOptions
            });

            // Listen for progress updates
            this.listenForProgress(tab.id);

        } catch (error) {
            console.error('Error starting export:', error);
            this.showError('Failed to start export: ' + error.message);
            this.isExporting = false;
        }
    }

    async listenForProgress(tabId) {
        const checkProgress = async () => {
            if (!this.isExporting) return;

            try {
                const response = await chrome.tabs.sendMessage(tabId, {
                    action: 'getExportProgress'
                });

                if (response) {
                    this.updateProgress(response);

                    if (response.status === 'completed') {
                        this.exportData = response.data;
                        this.showResults(response.summary);
                        this.isExporting = false;
                    } else if (response.status === 'error') {
                        this.showError(response.error);
                        this.isExporting = false;
                    } else {
                        // Continue checking progress
                        setTimeout(checkProgress, 1000);
                    }
                }
            } catch (error) {
                console.error('Error checking progress:', error);
                this.showError('Lost connection to export process');
                this.isExporting = false;
            }
        };

        checkProgress();
    }

    updateProgress(progressData) {
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        const progressStats = document.getElementById('progress-stats');
        const currentPageTitle = document.getElementById('current-page-title');

        const percentage = (progressData.completed / progressData.total) * 100;
        progressFill.style.width = `${percentage}%`;
        
        progressText.textContent = progressData.message || 'Exporting pages...';
        progressStats.textContent = `${progressData.completed} / ${progressData.total} pages`;
        currentPageTitle.textContent = progressData.currentPage || '-';
    }

    cancelExport() {
        this.isExporting = false;
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.tabs.sendMessage(tabs[0].id, { action: 'cancelExport' });
        });
        this.resetToInitialState();
    }

    showProgressSection() {
        document.getElementById('export-options').style.display = 'none';
        document.getElementById('space-selection').style.display = 'none';
        document.querySelector('.action-section').style.display = 'none';
        document.getElementById('progress-section').style.display = 'block';
        document.getElementById('error-section').style.display = 'none';
        document.getElementById('results-section').style.display = 'none';
    }

    showResults(summary) {
        document.getElementById('progress-section').style.display = 'none';
        document.getElementById('results-section').style.display = 'block';
        
        const summaryElement = document.getElementById('results-summary');
        summaryElement.innerHTML = `
            <p><strong>Export completed successfully!</strong></p>
            <p>Pages exported: ${summary.totalPages}</p>
            <p>Spaces: ${summary.totalSpaces}</p>
            <p>Format: ${summary.format.toUpperCase()}</p>
            <p>Size: ${this.formatFileSize(summary.totalSize)}</p>
        `;
    }

    showError(message) {
        document.getElementById('error-section').style.display = 'block';
        document.getElementById('error-message').textContent = message;
        
        // Hide other sections
        document.getElementById('progress-section').style.display = 'none';
        document.getElementById('results-section').style.display = 'none';
    }

    async downloadExport() {
        if (!this.exportData) return;

        try {
            // Create and download the export file
            const blob = new Blob([this.exportData], { type: 'application/zip' });
            const url = URL.createObjectURL(blob);
            
            await chrome.downloads.download({
                url: url,
                filename: `confluence-export-${new Date().toISOString().split('T')[0]}.zip`,
                saveAs: true
            });

            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error downloading export:', error);
            this.showError('Failed to download export file');
        }
    }

    resetToInitialState() {
        this.isExporting = false;
        this.exportData = null;
        
        // Reset UI
        document.getElementById('export-options').style.display = 'block';
        document.getElementById('space-selection').style.display = 'block';
        document.querySelector('.action-section').style.display = 'block';
        document.getElementById('progress-section').style.display = 'none';
        document.getElementById('error-section').style.display = 'none';
        document.getElementById('results-section').style.display = 'none';
        
        // Reset progress
        document.getElementById('progress-fill').style.width = '0%';
        document.getElementById('progress-text').textContent = 'Preparing export...';
        document.getElementById('progress-stats').textContent = '0 / 0 pages';
        document.getElementById('current-page-title').textContent = '-';
    }

    loadSettings() {
        // Load saved settings from storage
        chrome.storage.sync.get(['exportSettings'], (result) => {
            if (result.exportSettings) {
                const settings = result.exportSettings;
                document.getElementById('export-format').value = settings.format || 'html';
                document.getElementById('include-attachments').checked = settings.includeAttachments !== false;
                document.getElementById('preserve-hierarchy').checked = settings.preserveHierarchy !== false;
                document.getElementById('rate-limit').value = settings.rateLimit || 3;
                document.getElementById('rate-limit-value').textContent = settings.rateLimit || 3;
            }
        });
    }

    saveSettings() {
        const settings = {
            format: document.getElementById('export-format').value,
            includeAttachments: document.getElementById('include-attachments').checked,
            preserveHierarchy: document.getElementById('preserve-hierarchy').checked,
            rateLimit: parseInt(document.getElementById('rate-limit').value)
        };

        chrome.storage.sync.set({ exportSettings: settings });
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ConfluenceExporterPopup();
});
