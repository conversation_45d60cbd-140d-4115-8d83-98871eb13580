// DOM Parser for Confluence web scraping
console.log('📦 Loading ConfluenceDOMParser...');

class ConfluenceDOMParser {
    constructor() {
        this.baseUrl = '';
        this.currentDomain = '';
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.rateLimit = 3; // requests per second
        this.lastRequestTime = 0;
        this.visitedUrls = new Set();
        this.discoveredSpaces = new Map();
        this.discoveredPages = new Map();
        
        this.init();
    }

    init() {
        this.detectConfluenceInstance();
    }

    detectConfluenceInstance() {
        // Extract base URL and domain info
        this.currentDomain = window.location.hostname;
        this.baseUrl = `${window.location.protocol}//${window.location.hostname}`;
        
        // Add context path if present
        const pathParts = window.location.pathname.split('/');
        if (pathParts.includes('wiki')) {
            const wikiIndex = pathParts.indexOf('wiki');
            this.baseUrl += '/' + pathParts.slice(1, wikiIndex + 1).join('/');
        }
    }

    // Set rate limit for page requests
    setRateLimit(requestsPerSecond) {
        this.rateLimit = requestsPerSecond;
    }

    // Check if current page is Confluence
    isConfluencePage() {
        console.log('🔍 Checking if page is Confluence...');
        console.log('Current URL:', window.location.href);
        console.log('Hostname:', window.location.hostname);

        const indicators = [
            // Meta tags
            document.querySelector('meta[name="confluence-base-url"]'),
            document.querySelector('meta[name="ajs-base-url"]'),
            document.querySelector('meta[name="application-name"][content*="Confluence"]'),
            // Confluence-specific elements
            document.querySelector('#confluence-navigation'),
            document.querySelector('.confluence-content'),
            document.querySelector('#main-content.wiki-content'),
            document.querySelector('[data-testid="confluence-navigation"]'),
            document.querySelector('.css-1dbjc4n'), // Modern Confluence React elements
            // JavaScript variables
            window.AJS && window.AJS.Meta,
            window.WRM, // Atlassian Web Resource Manager
            // URL patterns
            window.location.href.includes('/wiki/'),
            window.location.href.includes('/confluence/'),
            window.location.pathname.startsWith('/wiki'),
            // Page title patterns
            document.title.includes('Confluence'),
            document.title.includes('Atlassian'),
            // Body classes and attributes
            document.body.classList.contains('confluence'),
            document.body.classList.contains('wiki-content'),
            document.body.hasAttribute('data-confluence'),
            // Atlassian domain
            window.location.hostname.includes('atlassian.net'),
            window.location.hostname.includes('atlassian.com'),
            // Modern Confluence indicators
            document.querySelector('[data-testid]'), // React test IDs common in modern Confluence
            document.querySelector('.css-'), // Emotion CSS classes used by Atlassian
        ];

        // Log each indicator for debugging
        indicators.forEach((indicator, index) => {
            if (indicator) {
                console.log(`✅ Indicator ${index} found:`, indicator);
            }
        });

        const isConfluence = indicators.some(indicator => indicator);
        console.log('🎯 Is Confluence page:', isConfluence);

        return isConfluence;
    }

    // Get instance type
    getInstanceType() {
        if (window.location.hostname.includes('.atlassian.net')) {
            return 'Confluence Cloud';
        } else {
            return 'Confluence Server/Data Center';
        }
    }

    // Discover all spaces by navigating to spaces directory
    async discoverSpaces() {
        try {
            // Try multiple space discovery methods
            const spacesUrls = [
                `${this.baseUrl}/wiki/spaces`,
                `${this.baseUrl}/wiki/spacedirectory/view.action`,
                `${this.baseUrl}/wiki/dashboard.action`
            ];

            for (const url of spacesUrls) {
                try {
                    const spaces = await this.parseSpacesFromUrl(url);
                    if (spaces.length > 0) {
                        return spaces;
                    }
                } catch (error) {
                    console.log(`Failed to get spaces from ${url}:`, error);
                    continue;
                }
            }

            // Fallback: parse spaces from current page navigation
            return this.parseSpacesFromCurrentPage();
        } catch (error) {
            console.error('Error discovering spaces:', error);
            return [];
        }
    }

    // Parse spaces from a specific URL
    async parseSpacesFromUrl(url) {
        const doc = await this.fetchPageDocument(url);
        return this.extractSpacesFromDocument(doc);
    }

    // Parse spaces from current page navigation
    parseSpacesFromCurrentPage() {
        const spaces = [];
        
        // Look for space navigation elements
        const spaceLinks = document.querySelectorAll([
            'a[href*="/wiki/spaces/"]',
            'a[href*="/display/"]',
            '.space-list a',
            '.spaces-list a',
            '#space-menu a'
        ].join(', '));

        spaceLinks.forEach(link => {
            const space = this.extractSpaceFromLink(link);
            if (space && !spaces.find(s => s.key === space.key)) {
                spaces.push(space);
            }
        });

        // Also check for spaces in breadcrumbs or current page
        const currentSpace = this.getCurrentSpaceInfo();
        if (currentSpace && !spaces.find(s => s.key === currentSpace.key)) {
            spaces.push(currentSpace);
        }

        return spaces;
    }

    // Extract space information from a link element
    extractSpaceFromLink(link) {
        const href = link.getAttribute('href');
        if (!href) return null;

        let spaceKey = null;
        let spaceName = link.textContent.trim();

        // Extract space key from different URL patterns
        const patterns = [
            /\/wiki\/spaces\/([^\/]+)/,
            /\/display\/([^\/]+)/,
            /spaceKey=([^&]+)/
        ];

        for (const pattern of patterns) {
            const match = href.match(pattern);
            if (match) {
                spaceKey = match[1];
                break;
            }
        }

        if (!spaceKey) return null;

        return {
            key: spaceKey,
            name: spaceName || spaceKey,
            url: this.resolveUrl(href),
            type: 'global' // Default type
        };
    }

    // Get current space information from page
    getCurrentSpaceInfo() {
        // Try to extract from breadcrumbs
        const breadcrumb = document.querySelector('.aui-nav-breadcrumbs, .breadcrumbs');
        if (breadcrumb) {
            const spaceLink = breadcrumb.querySelector('a[href*="/display/"], a[href*="/spaces/"]');
            if (spaceLink) {
                return this.extractSpaceFromLink(spaceLink);
            }
        }

        // Try to extract from page URL
        const urlPatterns = [
            /\/wiki\/spaces\/([^\/]+)/,
            /\/display\/([^\/]+)/
        ];

        for (const pattern of urlPatterns) {
            const match = window.location.href.match(pattern);
            if (match) {
                return {
                    key: match[1],
                    name: match[1], // Will be updated when we visit the space
                    url: `${this.baseUrl}/wiki/spaces/${match[1]}`,
                    type: 'global'
                };
            }
        }

        return null;
    }

    // Discover all pages in a space
    async discoverSpacePages(spaceKey) {
        try {
            const pages = [];
            
            // Try different page discovery methods
            const pageUrls = [
                `${this.baseUrl}/wiki/spaces/${spaceKey}/pages`,
                `${this.baseUrl}/wiki/spaces/${spaceKey}`,
                `${this.baseUrl}/wiki/display/${spaceKey}`
            ];

            for (const url of pageUrls) {
                try {
                    const spacePages = await this.parsePagesFromUrl(url, spaceKey);
                    pages.push(...spacePages);
                    
                    // If we found pages, also look for more through navigation
                    if (spacePages.length > 0) {
                        const additionalPages = await this.discoverAdditionalPages(url, spaceKey);
                        pages.push(...additionalPages);
                    }
                    
                    break; // Success, no need to try other URLs
                } catch (error) {
                    console.log(`Failed to get pages from ${url}:`, error);
                    continue;
                }
            }

            // Remove duplicates
            const uniquePages = pages.filter((page, index, self) => 
                index === self.findIndex(p => p.id === page.id)
            );

            return uniquePages;
        } catch (error) {
            console.error(`Error discovering pages for space ${spaceKey}:`, error);
            return [];
        }
    }

    // Parse pages from a specific URL
    async parsePagesFromUrl(url, spaceKey) {
        const doc = await this.fetchPageDocument(url);
        return this.extractPagesFromDocument(doc, spaceKey);
    }

    // Extract pages from document
    extractPagesFromDocument(doc, spaceKey) {
        const pages = [];
        
        // Look for page links in various locations
        const pageSelectors = [
            'a[href*="/wiki/spaces/"][href*="/pages/"]',
            'a[href*="/display/"]',
            '.page-tree a',
            '.content-tree a',
            '.space-content a',
            '.page-list a'
        ];

        pageSelectors.forEach(selector => {
            const links = doc.querySelectorAll(selector);
            links.forEach(link => {
                const page = this.extractPageFromLink(link, spaceKey);
                if (page && !pages.find(p => p.id === page.id)) {
                    pages.push(page);
                }
            });
        });

        return pages;
    }

    // Extract page information from a link element
    extractPageFromLink(link, spaceKey) {
        const href = link.getAttribute('href');
        if (!href) return null;

        let pageId = null;
        let pageTitle = link.textContent.trim();

        // Extract page ID from different URL patterns
        const patterns = [
            /\/wiki\/spaces\/[^\/]+\/pages\/(\d+)/,
            /\/display\/[^\/]+\/([^\/\?]+)/,
            /pageId=(\d+)/
        ];

        for (const pattern of patterns) {
            const match = href.match(pattern);
            if (match) {
                pageId = match[1];
                break;
            }
        }

        // If no numeric ID found, use URL-based ID
        if (!pageId) {
            pageId = this.generatePageIdFromUrl(href);
        }

        if (!pageId || !pageTitle) return null;

        return {
            id: pageId,
            title: pageTitle,
            url: this.resolveUrl(href),
            spaceKey: spaceKey,
            type: 'page'
        };
    }

    // Generate a consistent page ID from URL
    generatePageIdFromUrl(url) {
        // Create a hash-based ID from the URL
        let hash = 0;
        for (let i = 0; i < url.length; i++) {
            const char = url.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString();
    }

    // Discover additional pages through navigation
    async discoverAdditionalPages(baseUrl, spaceKey) {
        const additionalPages = [];
        
        try {
            // Look for pagination or "show more" links
            const doc = await this.fetchPageDocument(baseUrl);
            
            const paginationLinks = doc.querySelectorAll([
                'a[href*="start="]',
                'a[href*="page="]',
                '.pagination a',
                '.show-more a'
            ].join(', '));

            for (const link of paginationLinks) {
                if (this.visitedUrls.has(link.href)) continue;
                
                try {
                    const pages = await this.parsePagesFromUrl(link.href, spaceKey);
                    additionalPages.push(...pages);
                    await this.rateLimitDelay();
                } catch (error) {
                    console.log(`Error parsing additional pages from ${link.href}:`, error);
                }
            }
        } catch (error) {
            console.log('Error discovering additional pages:', error);
        }

        return additionalPages;
    }

    // Fetch and parse a page document
    async fetchPageDocument(url) {
        if (this.visitedUrls.has(url)) {
            throw new Error('URL already visited');
        }

        await this.rateLimitDelay();
        this.visitedUrls.add(url);

        try {
            const response = await fetch(url, {
                credentials: 'same-origin',
                headers: {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const html = await response.text();
            const parser = new DOMParser();
            return parser.parseFromString(html, 'text/html');
        } catch (error) {
            console.error(`Error fetching ${url}:`, error);
            throw error;
        }
    }

    // Parse page content from current page
    parseCurrentPageContent() {
        const content = {
            id: this.extractPageIdFromCurrentUrl(),
            title: this.extractPageTitle(),
            body: this.extractPageBody(),
            breadcrumbs: this.extractBreadcrumbs(),
            attachments: this.extractAttachments(),
            metadata: this.extractPageMetadata(),
            spaceKey: this.getCurrentSpaceKey()
        };

        return content;
    }

    // Extract page ID from current URL
    extractPageIdFromCurrentUrl() {
        const patterns = [
            /\/wiki\/spaces\/[^\/]+\/pages\/(\d+)/,
            /pageId=(\d+)/
        ];

        for (const pattern of patterns) {
            const match = window.location.href.match(pattern);
            if (match) {
                return match[1];
            }
        }

        return this.generatePageIdFromUrl(window.location.href);
    }

    // Extract page title
    extractPageTitle() {
        // Try multiple selectors for page title
        const titleSelectors = [
            '#title-text',
            '.page-title',
            'h1.title',
            '.content-title',
            '.wiki-content h1'
        ];

        for (const selector of titleSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                return element.textContent.trim();
            }
        }

        // Fallback to document title
        const docTitle = document.title;
        return docTitle.replace(/ - .*$/, '').trim();
    }

    // Extract page body content
    extractPageBody() {
        // Try multiple selectors for main content
        const contentSelectors = [
            '#main-content .wiki-content',
            '.wiki-content',
            '.page-content',
            '.content-body',
            '#content .main-content'
        ];

        for (const selector of contentSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                return this.cleanupContent(element.innerHTML);
            }
        }

        return '';
    }

    // Clean up extracted content
    cleanupContent(html) {
        // Remove script tags and other unwanted elements
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        // Remove unwanted elements
        const unwantedSelectors = [
            'script',
            'style',
            '.edit-button',
            '.page-metadata',
            '.confluence-information-macro-information'
        ];

        unwantedSelectors.forEach(selector => {
            const elements = tempDiv.querySelectorAll(selector);
            elements.forEach(el => el.remove());
        });

        return tempDiv.innerHTML;
    }

    // Extract breadcrumbs
    extractBreadcrumbs() {
        const breadcrumbs = [];
        const breadcrumbSelectors = [
            '.aui-nav-breadcrumbs a',
            '.breadcrumbs a',
            '.page-breadcrumbs a'
        ];

        for (const selector of breadcrumbSelectors) {
            const links = document.querySelectorAll(selector);
            if (links.length > 0) {
                links.forEach(link => {
                    breadcrumbs.push({
                        title: link.textContent.trim(),
                        url: link.href
                    });
                });
                break;
            }
        }

        return breadcrumbs;
    }

    // Extract attachments
    extractAttachments() {
        const attachments = [];
        const attachmentSelectors = [
            '.attachment-list a',
            '.attachments a',
            'a[href*="/download/attachments/"]'
        ];

        attachmentSelectors.forEach(selector => {
            const links = document.querySelectorAll(selector);
            links.forEach(link => {
                const attachment = {
                    title: link.textContent.trim(),
                    url: link.href,
                    type: this.getFileTypeFromUrl(link.href)
                };
                attachments.push(attachment);
            });
        });

        return attachments;
    }

    // Extract page metadata
    extractPageMetadata() {
        return {
            lastModified: this.extractLastModified(),
            author: this.extractAuthor(),
            version: this.extractVersion(),
            labels: this.extractLabels()
        };
    }

    // Extract last modified date
    extractLastModified() {
        const metaSelectors = [
            '.last-modified time',
            '.page-metadata time',
            '[data-last-modified]'
        ];

        for (const selector of metaSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                return element.getAttribute('datetime') || element.textContent.trim();
            }
        }

        return null;
    }

    // Extract author information
    extractAuthor() {
        const authorSelectors = [
            '.author a',
            '.page-metadata .author',
            '[data-author]'
        ];

        for (const selector of authorSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                return element.textContent.trim();
            }
        }

        return null;
    }

    // Extract version information
    extractVersion() {
        const versionSelectors = [
            '.version-number',
            '[data-version]'
        ];

        for (const selector of versionSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                return element.textContent.trim();
            }
        }

        return null;
    }

    // Extract labels/tags
    extractLabels() {
        const labels = [];
        const labelSelectors = [
            '.labels a',
            '.page-labels a',
            '.content-labels a'
        ];

        labelSelectors.forEach(selector => {
            const links = document.querySelectorAll(selector);
            links.forEach(link => {
                labels.push(link.textContent.trim());
            });
        });

        return labels;
    }

    // Get current space key
    getCurrentSpaceKey() {
        const patterns = [
            /\/wiki\/spaces\/([^\/]+)/,
            /\/display\/([^\/]+)/
        ];

        for (const pattern of patterns) {
            const match = window.location.href.match(pattern);
            if (match) {
                return match[1];
            }
        }

        return null;
    }

    // Get file type from URL
    getFileTypeFromUrl(url) {
        const extension = url.split('.').pop().toLowerCase();
        const typeMap = {
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt': 'application/vnd.ms-powerpoint',
            'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'svg': 'image/svg+xml'
        };

        return typeMap[extension] || 'application/octet-stream';
    }

    // Resolve relative URLs to absolute
    resolveUrl(url) {
        if (url.startsWith('http')) {
            return url;
        }
        
        if (url.startsWith('/')) {
            return `${window.location.protocol}//${window.location.hostname}${url}`;
        }
        
        return `${this.baseUrl}${url.startsWith('/') ? '' : '/'}${url}`;
    }

    // Rate limiting implementation
    async rateLimitDelay() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        const minInterval = 1000 / this.rateLimit; // milliseconds between requests

        if (timeSinceLastRequest < minInterval) {
            const delay = minInterval - timeSinceLastRequest;
            await new Promise(resolve => setTimeout(resolve, delay));
        }

        this.lastRequestTime = Date.now();
    }

    // Navigate to a specific page and wait for it to load
    async navigateToPage(url) {
        return new Promise((resolve, reject) => {
            // Create a temporary iframe to load the page
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = url;
            
            iframe.onload = () => {
                try {
                    const doc = iframe.contentDocument || iframe.contentWindow.document;
                    resolve(doc);
                } catch (error) {
                    reject(new Error('Cannot access iframe content - likely CORS restriction'));
                } finally {
                    document.body.removeChild(iframe);
                }
            };
            
            iframe.onerror = () => {
                document.body.removeChild(iframe);
                reject(new Error(`Failed to load ${url}`));
            };
            
            document.body.appendChild(iframe);
            
            // Timeout after 30 seconds
            setTimeout(() => {
                if (document.body.contains(iframe)) {
                    document.body.removeChild(iframe);
                    reject(new Error(`Timeout loading ${url}`));
                }
            }, 30000);
        });
    }
}

// Make ConfluenceDOMParser globally accessible
window.ConfluenceDOMParser = ConfluenceDOMParser;

// Log that the DOM parser is loaded
console.log('✅ ConfluenceDOMParser loaded and available globally');
