{"manifest_version": 3, "name": "Confluence Documentation Exporter", "version": "1.0.0", "description": "Export all Confluence documentation from any Confluence instance", "permissions": ["activeTab", "storage", "downloads"], "host_permissions": ["*://*/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["*://*/*"], "js": ["content.js", "confluence-api.js", "export-utils.js"], "run_at": "document_idle"}], "action": {"default_popup": "popup.html", "default_title": "Export Confluence Documentation", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["export-utils.js", "confluence-api.js"], "matches": ["*://*/*"]}]}