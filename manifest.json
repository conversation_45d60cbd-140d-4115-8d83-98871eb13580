{"manifest_version": 3, "name": "Confluence Documentation Exporter", "version": "1.0.0", "description": "Export all Confluence documentation by parsing web pages directly", "permissions": ["activeTab", "storage", "downloads", "tabs"], "host_permissions": ["*://izberg.atlassian.net/*", "*://*.atlassian.net/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["*://izberg.atlassian.net/*", "*://*.atlassian.net/*", "*://izberg.atlassian.net/wiki/*", "*://izberg.atlassian.net/wiki/home*"], "js": ["content.js", "dom-parser.js", "export-utils.js"], "run_at": "document_idle", "all_frames": false}], "action": {"default_popup": "popup.html", "default_title": "Export Confluence Documentation", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["export-utils.js", "dom-parser.js"], "matches": ["*://izberg.atlassian.net/*", "*://*.atlassian.net/*"]}]}