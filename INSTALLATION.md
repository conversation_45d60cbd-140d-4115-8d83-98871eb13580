# Installation Guide - DOM-Based Confluence Exporter

## Quick Start

### 1. Prepare the Extension

First, ensure you have all the required files:

```bash
# Check that all files are present
ls -la
# Should show: manifest.json, popup.html, popup.css, popup.js, content.js, background.js, dom-parser.js, export-utils.js, icons/
```

### 2. Create Icon Files

The extension requires icon files. Create simple placeholder icons:

**Option A: Using ImageMagick (if installed)**
```bash
# Create simple blue square icons
convert -size 16x16 xc:"#0052cc" icons/icon16.png
convert -size 48x48 xc:"#0052cc" icons/icon48.png
convert -size 128x128 xc:"#0052cc" icons/icon128.png
```

**Option B: Download placeholder icons**
```bash
# Create simple colored squares (requires curl)
curl -o icons/icon16.png "https://via.placeholder.com/16x16/0052cc/ffffff?text=C"
curl -o icons/icon48.png "https://via.placeholder.com/48x48/0052cc/ffffff?text=C"
curl -o icons/icon128.png "https://via.placeholder.com/128x128/0052cc/ffffff?text=C"
```

**Option C: Manual creation**
- Use any image editor to create 16x16, 48x48, and 128x128 PNG files
- Save them as `icon16.png`, `icon48.png`, and `icon128.png` in the `icons/` folder

### 3. Load Extension in Chrome

1. **Open Chrome Extensions Page**
   - Navigate to `chrome://extensions/`
   - Or: Menu → More Tools → Extensions

2. **Enable Developer Mode**
   - Toggle the "Developer mode" switch in the top-right corner

3. **Load Unpacked Extension**
   - Click "Load unpacked" button
   - Select the folder containing the extension files
   - The extension should appear in the list

4. **Pin Extension (Optional)**
   - Click the puzzle piece icon in Chrome toolbar
   - Find "Confluence Documentation Exporter"
   - Click the pin icon to keep it visible

### 4. Test the Extension

1. **Navigate to Confluence**
   - Go to izberg.atlassian.net (or your Confluence Cloud instance)
   - The extension icon should become active

2. **Open Extension Popup**
   - Click the extension icon
   - Should show "✅ Confluence detected: Confluence Cloud"

3. **Test Space Discovery**
   - The extension should automatically discover available spaces
   - You should see a list of spaces you have access to

4. **Test Export (Optional)**
   - Select a small space for testing
   - Set rate limit to 1-2 requests/second for initial testing
   - Try exporting a few pages to verify functionality

## Detailed Installation Steps

### Prerequisites

- **Chrome Browser**: Version 88+ (Manifest V3 support)
- **Confluence Access**: Valid account with web access to izberg.atlassian.net
- **Local Files**: All extension files in a single directory
- **No API Permissions Required**: Works through web interface parsing

### File Verification

Ensure your directory structure looks like this:

```
confluence-exporter/
├── manifest.json
├── popup.html
├── popup.css
├── popup.js
├── content.js
├── background.js
├── dom-parser.js
├── export-utils.js
├── icons/
│   ├── icon16.png
│   ├── icon48.png
│   └── icon128.png
├── README.md
└── INSTALLATION.md
```

### Chrome Extension Loading

#### Step 1: Access Extensions Page
- **Method 1**: Type `chrome://extensions/` in address bar
- **Method 2**: Chrome Menu → More Tools → Extensions
- **Method 3**: Right-click extension icon → Manage Extensions

#### Step 2: Enable Developer Mode
- Look for "Developer mode" toggle in top-right
- Click to enable (should turn blue/green)
- New buttons will appear: "Load unpacked", "Pack extension", "Update"

#### Step 3: Load the Extension
- Click "Load unpacked" button
- Navigate to your extension directory
- Select the folder (not individual files)
- Click "Select Folder" or "Open"

#### Step 4: Verify Installation
- Extension should appear in the list with:
  - Name: "Confluence Documentation Exporter"
  - Version: "1.0.0"
  - Status: Enabled
- Extension icon should appear in Chrome toolbar

### Troubleshooting Installation

#### Common Issues

**1. "Manifest file is missing or unreadable"**
- Ensure `manifest.json` is in the root directory
- Check file permissions (should be readable)
- Verify JSON syntax is valid

**2. "Icon file not found"**
- Create the required icon files in `icons/` directory
- Ensure correct filenames: `icon16.png`, `icon48.png`, `icon128.png`
- Check file permissions

**3. "Extension failed to load"**
- Check browser console for detailed errors
- Verify all JavaScript files are present
- Ensure no syntax errors in code files

**4. Extension loads but doesn't work**
- Check if you're on izberg.atlassian.net
- Verify you're logged into Confluence
- Check browser console for runtime errors
- Ensure you're on a page with space navigation visible

#### Validation Commands

```bash
# Check file structure
find . -name "*.js" -o -name "*.json" -o -name "*.html" -o -name "*.css" -o -name "*.png"

# Validate manifest.json syntax
python -m json.tool manifest.json

# Check file permissions
ls -la *.js *.json *.html *.css icons/*.png
```

### Development Mode Features

When loaded as an unpacked extension, you get additional features:

- **Live Reload**: Changes to files are reflected after clicking "Reload" button
- **Error Details**: Detailed error messages in Extensions page
- **Console Access**: Debug with browser developer tools
- **Source Inspection**: View and edit source files directly

### Security Considerations

- **Permissions**: Extension requests minimal necessary permissions
- **Local Only**: No external data transmission
- **Session-Based**: Uses existing Confluence authentication
- **No Storage**: Doesn't store sensitive data permanently
- **DOM Parsing**: Works through web interface, no API access required
- **Rate Limiting**: Prevents overwhelming servers and potential blocking

### Next Steps

After successful installation:

1. **Test on Confluence**: Navigate to izberg.atlassian.net
2. **Configure Settings**: Adjust rate limiting (start with 1-2 req/sec) and export options
3. **Try Small Export**: Test with a single space first
4. **Review Output**: Check exported HTML/JSON format
5. **Scale Up**: Export larger spaces as needed (increase rate limit gradually)

### Uninstallation

To remove the extension:

1. Go to `chrome://extensions/`
2. Find "Confluence Documentation Exporter"
3. Click "Remove" button
4. Confirm removal

The extension files remain on your computer and can be reloaded anytime.

### Updates

To update the extension:

1. Modify the source files as needed
2. Go to `chrome://extensions/`
3. Find your extension
4. Click the "Reload" button (circular arrow icon)
5. Test the updated functionality

For version updates, increment the version number in `manifest.json` before reloading.
