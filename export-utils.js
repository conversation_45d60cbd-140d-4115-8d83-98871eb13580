// Export utilities for processing and formatting Confluence content
class ExportUtils {
    constructor() {
        this.exportFormats = ['html', 'json', 'markdown'];
        this.supportedAttachmentTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/svg+xml',
            'application/pdf', 'text/plain', 'text/html',
            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
    }

    // Create HTML export package
    async createHtmlExport(pages, options = {}) {
        const exportPackage = {
            metadata: this.createExportMetadata(pages, 'html', options),
            files: []
        };

        // Create index.html
        const indexHtml = this.generateIndexHtml(pages, options);
        exportPackage.files.push({
            path: 'index.html',
            content: indexHtml,
            type: 'text/html'
        });

        // Create individual page files
        for (const page of pages) {
            const pageHtml = await this.generatePageHtml(page, options);
            const fileName = this.sanitizeFileName(page.title) + '.html';
            
            exportPackage.files.push({
                path: `pages/${fileName}`,
                content: pageHtml,
                type: 'text/html',
                pageId: page.id,
                spaceKey: page.space?.key
            });
        }

        // Add CSS styles
        const cssContent = this.generateCssStyles();
        exportPackage.files.push({
            path: 'styles/confluence-export.css',
            content: cssContent,
            type: 'text/css'
        });

        // Process attachments if requested
        if (options.includeAttachments) {
            await this.processAttachments(pages, exportPackage, options);
        }

        return exportPackage;
    }

    // Create JSON export package
    createJsonExport(pages, options = {}) {
        const exportData = {
            metadata: this.createExportMetadata(pages, 'json', options),
            spaces: this.groupPagesBySpace(pages),
            pages: pages.map(page => this.sanitizePageForExport(page, options))
        };

        if (options.preserveHierarchy) {
            exportData.hierarchy = this.buildPageHierarchy(pages);
        }

        return JSON.stringify(exportData, null, 2);
    }

    // Create export metadata
    createExportMetadata(pages, format, options) {
        const spaces = [...new Set(pages.map(page => page.space?.key).filter(Boolean))];
        
        return {
            exportDate: new Date().toISOString(),
            format: format,
            version: '1.0.0',
            totalPages: pages.length,
            totalSpaces: spaces.length,
            spaces: spaces,
            options: {
                includeAttachments: options.includeAttachments || false,
                preserveHierarchy: options.preserveHierarchy || false,
                rateLimit: options.rateLimit || 3
            },
            generator: 'Confluence Documentation Exporter'
        };
    }

    // Generate index.html for HTML export
    generateIndexHtml(pages, options) {
        const spaces = this.groupPagesBySpace(pages);
        const spaceKeys = Object.keys(spaces);

        let html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confluence Export - Index</title>
    <link rel="stylesheet" href="styles/confluence-export.css">
</head>
<body>
    <div class="container">
        <header class="export-header">
            <h1>📚 Confluence Documentation Export</h1>
            <p class="export-info">
                Exported on ${new Date().toLocaleDateString()} | 
                ${pages.length} pages across ${spaceKeys.length} spaces
            </p>
        </header>

        <nav class="space-navigation">
            <h2>Spaces</h2>
            <ul class="space-list">`;

        spaceKeys.forEach(spaceKey => {
            const spacePages = spaces[spaceKey];
            const spaceName = spacePages[0]?.space?.name || spaceKey;
            
            html += `
                <li class="space-item">
                    <a href="#space-${spaceKey}" class="space-link">
                        <strong>${spaceName}</strong>
                        <span class="page-count">(${spacePages.length} pages)</span>
                    </a>
                </li>`;
        });

        html += `
            </ul>
        </nav>

        <main class="content">`;

        // Generate space sections
        spaceKeys.forEach(spaceKey => {
            const spacePages = spaces[spaceKey];
            const spaceName = spacePages[0]?.space?.name || spaceKey;
            
            html += `
            <section id="space-${spaceKey}" class="space-section">
                <h2 class="space-title">${spaceName}</h2>
                <div class="page-list">`;

            if (options.preserveHierarchy) {
                html += this.generateHierarchicalPageList(spacePages);
            } else {
                html += this.generateFlatPageList(spacePages);
            }

            html += `
                </div>
            </section>`;
        });

        html += `
        </main>

        <footer class="export-footer">
            <p>Generated by Confluence Documentation Exporter</p>
        </footer>
    </div>

    <script>
        // Simple navigation enhancement
        document.querySelectorAll('.space-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(link.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    </script>
</body>
</html>`;

        return html;
    }

    // Generate individual page HTML
    async generatePageHtml(page, options) {
        const pageTitle = page.title || 'Untitled Page';
        const spaceInfo = page.space ? `${page.space.name} (${page.space.key})` : 'Unknown Space';
        
        let html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle}</title>
    <link rel="stylesheet" href="../styles/confluence-export.css">
</head>
<body>
    <div class="container">
        <header class="page-header">
            <nav class="breadcrumb">
                <a href="../index.html">📚 Export Home</a>`;

        // Add breadcrumb for ancestors
        if (page.ancestors && page.ancestors.length > 0) {
            page.ancestors.forEach(ancestor => {
                const ancestorFileName = this.sanitizeFileName(ancestor.title) + '.html';
                html += ` > <a href="${ancestorFileName}">${ancestor.title}</a>`;
            });
        }

        html += ` > <span class="current-page">${pageTitle}</span>
            </nav>
            
            <h1 class="page-title">${pageTitle}</h1>
            
            <div class="page-meta">
                <span class="space-info">Space: ${spaceInfo}</span>
                <span class="version-info">Version: ${page.version?.number || 'Unknown'}</span>
                <span class="last-modified">Modified: ${page.version?.when || 'Unknown'}</span>
            </div>
        </header>

        <main class="page-content">
            ${this.processConfluenceContent(page.exportedContent?.body || page.body?.storage?.value || '')}
        </main>`;

        // Add attachments section if present
        if (page.exportedContent?.attachments && page.exportedContent.attachments.length > 0) {
            html += `
        <section class="attachments-section">
            <h2>📎 Attachments</h2>
            <ul class="attachment-list">`;

            page.exportedContent.attachments.forEach(attachment => {
                const attachmentPath = `../attachments/${this.sanitizeFileName(attachment.title)}`;
                html += `
                <li class="attachment-item">
                    <a href="${attachmentPath}" class="attachment-link">
                        ${attachment.title}
                        <span class="attachment-size">(${this.formatFileSize(attachment.extensions?.fileSize || 0)})</span>
                    </a>
                </li>`;
            });

            html += `
            </ul>
        </section>`;
        }

        html += `
        <footer class="page-footer">
            <a href="../index.html" class="back-link">← Back to Index</a>
        </footer>
    </div>
</body>
</html>`;

        return html;
    }

    // Process Confluence storage format content
    processConfluenceContent(content) {
        if (!content) return '<p>No content available</p>';

        // Basic processing of Confluence storage format
        // This is a simplified version - a full implementation would need more comprehensive parsing
        
        // Convert Confluence macros to HTML
        content = content.replace(
            /<ac:structured-macro[^>]*ac:name="([^"]*)"[^>]*>(.*?)<\/ac:structured-macro>/gs,
            (match, macroName, macroContent) => {
                return this.processMacro(macroName, macroContent);
            }
        );

        // Convert Confluence links
        content = content.replace(
            /<ac:link[^>]*>(.*?)<\/ac:link>/gs,
            (match, linkContent) => {
                return `<a href="#" class="confluence-link">${linkContent}</a>`;
            }
        );

        // Convert Confluence images
        content = content.replace(
            /<ac:image[^>]*>(.*?)<\/ac:image>/gs,
            (match, imageContent) => {
                return `<div class="confluence-image">${imageContent}</div>`;
            }
        );

        return content;
    }

    // Process Confluence macros
    processMacro(macroName, macroContent) {
        switch (macroName) {
            case 'info':
                return `<div class="macro-info">${macroContent}</div>`;
            case 'warning':
                return `<div class="macro-warning">${macroContent}</div>`;
            case 'note':
                return `<div class="macro-note">${macroContent}</div>`;
            case 'code':
                return `<pre class="macro-code">${macroContent}</pre>`;
            case 'toc':
                return `<div class="macro-toc">Table of Contents</div>`;
            default:
                return `<div class="macro-${macroName}">${macroContent}</div>`;
        }
    }

    // Generate CSS styles for HTML export
    generateCssStyles() {
        return `
/* Confluence Export Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 8px;
}

/* Header Styles */
.export-header, .page-header {
    border-bottom: 2px solid #0052cc;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.export-header h1, .page-title {
    color: #0052cc;
    font-size: 2.5em;
    margin-bottom: 10px;
}

.export-info, .page-meta {
    color: #666;
    font-size: 0.9em;
}

.page-meta span {
    margin-right: 20px;
}

/* Navigation Styles */
.breadcrumb {
    margin-bottom: 20px;
    font-size: 0.9em;
}

.breadcrumb a {
    color: #0052cc;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.current-page {
    font-weight: bold;
    color: #333;
}

/* Space Navigation */
.space-navigation {
    margin-bottom: 30px;
}

.space-list {
    list-style: none;
}

.space-item {
    margin-bottom: 10px;
}

.space-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    text-decoration: none;
    color: #333;
    transition: background-color 0.2s;
}

.space-link:hover {
    background: #e9ecef;
}

.page-count {
    color: #666;
    font-size: 0.9em;
}

/* Content Styles */
.content, .page-content {
    margin-bottom: 30px;
}

.space-section {
    margin-bottom: 40px;
}

.space-title {
    color: #0052cc;
    font-size: 1.8em;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

/* Page List Styles */
.page-list {
    margin-left: 20px;
}

.page-item {
    margin-bottom: 8px;
}

.page-link {
    color: #0052cc;
    text-decoration: none;
    font-weight: 500;
}

.page-link:hover {
    text-decoration: underline;
}

/* Macro Styles */
.macro-info {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
    padding: 15px;
    margin: 15px 0;
}

.macro-warning {
    background: #fff3e0;
    border-left: 4px solid #ff9800;
    padding: 15px;
    margin: 15px 0;
}

.macro-note {
    background: #f3e5f5;
    border-left: 4px solid #9c27b0;
    padding: 15px;
    margin: 15px 0;
}

.macro-code {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin: 15px 0;
    overflow-x: auto;
    font-family: 'Monaco', 'Consolas', monospace;
}

/* Attachment Styles */
.attachments-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

.attachment-list {
    list-style: none;
}

.attachment-item {
    margin-bottom: 10px;
}

.attachment-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    text-decoration: none;
    color: #333;
}

.attachment-link:hover {
    background: #e9ecef;
}

.attachment-size {
    color: #666;
    font-size: 0.9em;
}

/* Footer Styles */
.export-footer, .page-footer {
    border-top: 1px solid #ddd;
    padding-top: 20px;
    text-align: center;
    color: #666;
    font-size: 0.9em;
}

.back-link {
    color: #0052cc;
    text-decoration: none;
    font-weight: 500;
}

.back-link:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .export-header h1, .page-title {
        font-size: 2em;
    }
    
    .space-link {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .page-count {
        margin-top: 5px;
    }
}`;
    }

    // Group pages by space
    groupPagesBySpace(pages) {
        const spaces = {};
        
        pages.forEach(page => {
            const spaceKey = page.space?.key || 'unknown';
            if (!spaces[spaceKey]) {
                spaces[spaceKey] = [];
            }
            spaces[spaceKey].push(page);
        });

        return spaces;
    }

    // Build page hierarchy
    buildPageHierarchy(pages) {
        const pageMap = new Map();
        const rootPages = [];

        // Create map of all pages
        pages.forEach(page => {
            pageMap.set(page.id, {
                ...page,
                children: []
            });
        });

        // Build tree structure
        pages.forEach(page => {
            const pageNode = pageMap.get(page.id);
            
            if (page.ancestors && page.ancestors.length > 0) {
                const parentId = page.ancestors[page.ancestors.length - 1].id;
                const parent = pageMap.get(parentId);
                
                if (parent) {
                    parent.children.push(pageNode);
                } else {
                    rootPages.push(pageNode);
                }
            } else {
                rootPages.push(pageNode);
            }
        });

        return rootPages;
    }

    // Generate hierarchical page list HTML
    generateHierarchicalPageList(pages) {
        const hierarchy = this.buildPageHierarchy(pages);
        return this.renderPageTree(hierarchy);
    }

    // Render page tree recursively
    renderPageTree(pages, level = 0) {
        let html = '<ul class="page-tree">';
        
        pages.forEach(page => {
            const fileName = this.sanitizeFileName(page.title) + '.html';
            html += `
            <li class="page-item" style="margin-left: ${level * 20}px;">
                <a href="pages/${fileName}" class="page-link">${page.title}</a>`;
            
            if (page.children && page.children.length > 0) {
                html += this.renderPageTree(page.children, level + 1);
            }
            
            html += '</li>';
        });
        
        html += '</ul>';
        return html;
    }

    // Generate flat page list HTML
    generateFlatPageList(pages) {
        let html = '<ul class="page-list">';
        
        pages.forEach(page => {
            const fileName = this.sanitizeFileName(page.title) + '.html';
            html += `
            <li class="page-item">
                <a href="pages/${fileName}" class="page-link">${page.title}</a>
            </li>`;
        });
        
        html += '</ul>';
        return html;
    }

    // Process attachments for export
    async processAttachments(pages, exportPackage, options) {
        for (const page of pages) {
            if (page.exportedContent?.attachments) {
                for (const attachment of page.exportedContent.attachments) {
                    try {
                        // Only process supported attachment types
                        if (this.supportedAttachmentTypes.includes(attachment.metadata?.mediaType)) {
                            const fileName = this.sanitizeFileName(attachment.title);
                            
                            exportPackage.files.push({
                                path: `attachments/${fileName}`,
                                content: `[Attachment: ${attachment.title}]`, // Placeholder
                                type: attachment.metadata?.mediaType || 'application/octet-stream',
                                attachmentId: attachment.id,
                                originalTitle: attachment.title
                            });
                        }
                    } catch (error) {
                        console.error(`Error processing attachment ${attachment.title}:`, error);
                    }
                }
            }
        }
    }

    // Sanitize page data for export
    sanitizePageForExport(page, options) {
        const sanitized = {
            id: page.id,
            title: page.title,
            type: page.type,
            status: page.status,
            space: page.space ? {
                key: page.space.key,
                name: page.space.name,
                type: page.space.type
            } : null,
            version: page.version ? {
                number: page.version.number,
                when: page.version.when,
                by: page.version.by
            } : null,
            ancestors: page.ancestors || [],
            body: page.exportedContent?.body || page.body?.storage?.value || ''
        };

        if (options.includeAttachments && page.exportedContent?.attachments) {
            sanitized.attachments = page.exportedContent.attachments.map(att => ({
                id: att.id,
                title: att.title,
                mediaType: att.metadata?.mediaType,
                fileSize: att.extensions?.fileSize
            }));
        }

        return sanitized;
    }

    // Utility functions
    sanitizeFileName(fileName) {
        return fileName
            .replace(/[<>:"/\\|?*]/g, '_')
            .replace(/\s+/g, '_')
            .toLowerCase();
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}
